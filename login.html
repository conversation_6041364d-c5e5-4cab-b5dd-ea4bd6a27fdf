<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Log In — Konspect</title>

	<!-- Google Fonts: <PERSON><PERSON> (brand), <PERSON><PERSON> (body), <PERSON><PERSON><PERSON><PERSON> (headings) -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@600;700&family=Lato:wght@300;400;700;900&family=Merriweather:wght@700;900&display=swap" rel="stylesheet">

	<!-- Font Awesome (for icons) -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">

	<!-- Tailwind CSS CDN -->
	<script src="https://cdn.tailwindcss.com"></script>
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						brand: '#FF6039',
						ink: '#1D1D1D',
						muted: '#585D62',
						paper: '#FAFAFA'
					},
					fontFamily: {
						sans: ['Lato', 'ui-sans-serif', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
						serif: ['Merriweather', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
						fredoka: ['Fredoka', 'system-ui', 'sans-serif']
					}
				}
			}
		}
	</script>

	<style>
		:root { --brand: #FF6039; --ink:#1D1D1D; --muted:#585D62; --paper:#FAFAFA; }
		html { scroll-behavior: smooth; }
		body { font-family: Lato, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif; color: var(--muted); background-color: var(--paper); }
		h1, h2, h3, h4, h5, h6 { font-family: Merriweather, Georgia, Cambria, Times New Roman, Times, serif; color: var(--ink); }
		.brand-font { font-family: Fredoka, system-ui, sans-serif; }
		.shadow-card { box-shadow: 0 16px 40px rgba(16,24,40,.08), 0 4px 10px rgba(16,24,40,.06); }
		.btn { transition: transform .08s ease, box-shadow .2s ease; }
		.btn:active { transform: translateY(1px); }
		
		/* Form styling */
		.form-input {
			transition: border-color 0.2s ease, box-shadow 0.2s ease;
		}
		.form-input:focus {
			border-color: var(--brand);
			box-shadow: 0 0 0 3px rgba(255, 96, 57, 0.1);
		}
		.form-input.error {
			border-color: #ef4444;
			box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
		}
		.password-toggle {
			cursor: pointer;
			user-select: none;
		}
		.form-input:valid:not(:placeholder-shown) {
			border-color: #10b981;
		}
		.form-input:valid:not(:placeholder-shown):focus {
			border-color: #10b981;
			box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
		}
		.social-btn:hover {
			transform: translateY(-1px);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		}
	</style>
</head>

<body class="bg-paper antialiased">
	<!-- Simple Navbar -->
	<header class="border-b border-black/5 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70">
		<nav class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between" aria-label="Primary">
			<a href="index.html" class="flex items-center gap-2 group" aria-label="Konspect home">
				<span class="brand-font text-2xl font-bold tracking-tight text-brand">Konspect</span>
			</a>
			<div class="flex items-center gap-3">
				<span class="text-sm text-muted">Don't have an account?</span>
				<a href="register.html" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up</a>
			</div>
		</nav>
	</header>

	<main class="min-h-screen bg-paper">
		<section class="flex items-center justify-center min-h-[calc(100vh-80px)] px-6 py-12">
			<div class="w-full max-w-md">
				<!-- Login Form Card -->
				<article class="bg-white rounded-2xl border border-black/10 shadow-card p-8">
					<header class="text-center mb-8">
						<h1 class="text-2xl font-serif font-bold text-ink">Welcome Back</h1>
						<p class="mt-2 text-sm text-muted">Sign in to your Konspect account</p>
					</header>

					<form id="loginForm" class="space-y-6" novalidate>
						<!-- Email Field -->
						<div>
							<label for="email" class="block text-sm font-semibold text-ink mb-2">Business Email</label>
							<input 
								type="email" 
								id="email" 
								name="email" 
								required 
								class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
								placeholder="<EMAIL>"
								autocomplete="email"
							>
							<div id="emailError" class="mt-1 text-sm text-red-600 hidden"></div>
						</div>

						<!-- Password Field -->
						<div>
							<label for="password" class="block text-sm font-semibold text-ink mb-2">Password</label>
							<div class="relative">
								<input 
									type="password" 
									id="password" 
									name="password" 
									required 
									class="form-input w-full px-4 py-3 pr-12 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
									placeholder="Enter your password"
									autocomplete="current-password"
								>
								<button 
									type="button" 
									id="togglePassword" 
									class="password-toggle absolute right-3 top-1/2 -translate-y-1/2 text-muted hover:text-ink focus:outline-none focus:text-ink"
									aria-label="Toggle password visibility"
								>
									<i class="fa-solid fa-eye" id="eyeIcon"></i>
								</button>
							</div>
							<div id="passwordError" class="mt-1 text-sm text-red-600 hidden"></div>
						</div>

						<!-- Remember Me & Forgot Password -->
						<div class="flex items-center justify-between">
							<label class="flex items-center gap-2 cursor-pointer">
								<input type="checkbox" id="remember" name="remember" class="w-4 h-4 text-brand border-black/20 rounded focus:ring-brand/60">
								<span class="text-sm text-muted">Remember me</span>
							</label>
							<a href="#forgot-password" class="text-sm text-brand hover:underline focus:outline-none focus:underline">Forgot password?</a>
						</div>

						<!-- Submit Button -->
						<button 
							type="submit" 
							class="btn w-full inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white hover:bg-brand/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 disabled:opacity-50 disabled:cursor-not-allowed"
							id="submitBtn"
						>
							<span id="submitText">Sign In</span>
							<i id="submitSpinner" class="fa-solid fa-spinner fa-spin ml-2 hidden"></i>
						</button>
					</form>

					<!-- Divider -->
					<div class="my-8 flex items-center">
						<div class="flex-1 border-t border-black/10"></div>
						<span class="px-4 text-sm text-muted">Or continue with</span>
						<div class="flex-1 border-t border-black/10"></div>
					</div>

					<!-- Social Login -->
					<div class="grid grid-cols-2 gap-3">
						<button class="btn social-btn flex items-center justify-center gap-2 px-4 py-3 border border-black/20 rounded-lg text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 transition-all duration-200">
							<i class="fab fa-google text-lg"></i>
							<span class="text-sm font-semibold">Google</span>
						</button>
						<button class="btn social-btn flex items-center justify-center gap-2 px-4 py-3 border border-black/20 rounded-lg text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 transition-all duration-200">
							<i class="fab fa-microsoft text-lg"></i>
							<span class="text-sm font-semibold">Microsoft</span>
						</button>
					</div>
				</article>

				<!-- Sign Up Link -->
				<p class="mt-8 text-center text-sm text-muted">
					Don't have an account? 
					<a href="register.html" class="text-brand hover:underline focus:outline-none focus:underline font-semibold">Sign up for free</a>
				</p>
			</div>
		</section>
	</main>

	<!-- Scripts -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/js/all.min.js"></script>
	<script>
		// Password visibility toggle
		(function(){
			const toggleBtn = document.getElementById('togglePassword');
			const passwordInput = document.getElementById('password');
			const eyeIcon = document.getElementById('eyeIcon');
			
			if(!toggleBtn || !passwordInput || !eyeIcon) return;
			
			toggleBtn.addEventListener('click', () => {
				const isPassword = passwordInput.type === 'password';
				passwordInput.type = isPassword ? 'text' : 'password';
				eyeIcon.className = isPassword ? 'fa-solid fa-eye-slash' : 'fa-solid fa-eye';
			});
		})();

		// Form validation and submission
		(function(){
			const form = document.getElementById('loginForm');
			const emailInput = document.getElementById('email');
			const passwordInput = document.getElementById('password');
			const submitBtn = document.getElementById('submitBtn');
			const submitText = document.getElementById('submitText');
			const submitSpinner = document.getElementById('submitSpinner');
			
			if(!form) return;

			// Email validation
			function validateEmail(email) {
				const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
				return re.test(email);
			}

			// Show error
			function showError(input, message) {
				const errorEl = document.getElementById(input.name + 'Error');
				if(errorEl) {
					errorEl.textContent = message;
					errorEl.classList.remove('hidden');
				}
				input.classList.add('error');
			}

			// Clear error
			function clearError(input) {
				const errorEl = document.getElementById(input.name + 'Error');
				if(errorEl) {
					errorEl.classList.add('hidden');
				}
				input.classList.remove('error');
			}

			// Real-time validation
			emailInput.addEventListener('blur', () => {
				if(emailInput.value && !validateEmail(emailInput.value)) {
					showError(emailInput, 'Please enter a valid email address');
				} else {
					clearError(emailInput);
				}
			});

			emailInput.addEventListener('input', () => {
				if(emailInput.classList.contains('error')) {
					clearError(emailInput);
				}
			});

			passwordInput.addEventListener('input', () => {
				if(passwordInput.classList.contains('error')) {
					clearError(passwordInput);
				}
			});

			// Form submission
			form.addEventListener('submit', async (e) => {
				e.preventDefault();
				
				let isValid = true;

				// Validate email
				if(!emailInput.value) {
					showError(emailInput, 'Email is required');
					isValid = false;
				} else if(!validateEmail(emailInput.value)) {
					showError(emailInput, 'Please enter a valid email address');
					isValid = false;
				}

				// Validate password
				if(!passwordInput.value) {
					showError(passwordInput, 'Password is required');
					isValid = false;
				}

				if(!isValid) return;

				// Show loading state
				submitBtn.disabled = true;
				submitText.textContent = 'Signing In...';
				submitSpinner.classList.remove('hidden');

				// Simulate API call
				try {
					await new Promise(resolve => setTimeout(resolve, 2000));
					// Redirect to dashboard or show success
					alert('Login successful! (This is a demo)');
				} catch (error) {
					alert('Login failed. Please try again.');
				} finally {
					// Reset button state
					submitBtn.disabled = false;
					submitText.textContent = 'Sign In';
					submitSpinner.classList.add('hidden');
				}
			});
		})();
	</script>
</body>
</html>
