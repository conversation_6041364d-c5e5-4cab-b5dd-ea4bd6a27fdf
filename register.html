<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Sign Up — Konspect</title>

	<!-- Google Fonts: <PERSON><PERSON> (brand), <PERSON><PERSON> (body), <PERSON><PERSON><PERSON><PERSON> (headings) -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@600;700&family=Lato:wght@300;400;700;900&family=Merriweather:wght@700;900&display=swap" rel="stylesheet">

	<!-- Font Awesome (for icons) -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">

	<!-- Tailwind CSS CDN -->
	<script src="https://cdn.tailwindcss.com"></script>
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						brand: '#FF6039',
						ink: '#1D1D1D',
						muted: '#585D62',
						paper: '#FAFAFA'
					},
					fontFamily: {
						sans: ['Lato', 'ui-sans-serif', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
						serif: ['Merriweather', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
						fredoka: ['Fredoka', 'system-ui', 'sans-serif']
					}
				}
			}
		}
	</script>

	<style>
		:root { --brand: #FF6039; --ink:#1D1D1D; --muted:#585D62; --paper:#FAFAFA; }
		html { scroll-behavior: smooth; }
		body { font-family: Lato, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif; color: var(--muted); background-color: var(--paper); }
		h1, h2, h3, h4, h5, h6 { font-family: Merriweather, Georgia, Cambria, Times New Roman, Times, serif; color: var(--ink); }
		.brand-font { font-family: Fredoka, system-ui, sans-serif; }
		.shadow-card { box-shadow: 0 16px 40px rgba(16,24,40,.08), 0 4px 10px rgba(16,24,40,.06); }
		.btn { transition: transform .08s ease, box-shadow .2s ease; }
		.btn:active { transform: translateY(1px); }
		
		/* Form styling */
		.form-input, .form-select {
			transition: border-color 0.2s ease, box-shadow 0.2s ease;
		}
		.form-input:focus, .form-select:focus {
			border-color: var(--brand);
			box-shadow: 0 0 0 3px rgba(255, 96, 57, 0.1);
		}
		.form-input.error, .form-select.error {
			border-color: #ef4444;
			box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
		}
		.password-toggle {
			cursor: pointer;
			user-select: none;
		}
		.plan-card {
			transition: all 0.2s ease;
			cursor: pointer;
		}
		.plan-card.selected {
			border-color: var(--brand);
			box-shadow: 0 0 0 3px rgba(255, 96, 57, 0.1);
		}
		.billing-toggle {
			position: relative;
			width: 48px;
			height: 24px;
			background: #e5e7eb;
			border-radius: 12px;
			transition: background-color 0.2s ease;
			cursor: pointer;
		}
		.billing-toggle.active {
			background: var(--brand);
		}
		.billing-toggle::after {
			content: '';
			position: absolute;
			top: 2px;
			left: 2px;
			width: 20px;
			height: 20px;
			background: white;
			border-radius: 50%;
			transition: transform 0.2s ease;
		}
		.billing-toggle.active::after {
			transform: translateX(24px);
		}
		.step-indicator {
			transition: all 0.3s ease;
		}
		.step-indicator.active {
			background: var(--brand);
			color: white;
		}
		.step-indicator.completed {
			background: var(--brand);
			color: white;
		}
		.step-content {
			animation: fadeIn 0.3s ease-in-out;
		}
		@keyframes fadeIn {
			from { opacity: 0; transform: translateY(10px); }
			to { opacity: 1; transform: translateY(0); }
		}
		.form-input:valid:not(:placeholder-shown) {
			border-color: #10b981;
		}
		.form-input:valid:not(:placeholder-shown):focus {
			border-color: #10b981;
			box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
		}
	</style>
</head>

<body class="bg-paper antialiased">
	<!-- Simple Navbar -->
	<header class="border-b border-black/5 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70">
		<nav class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between" aria-label="Primary">
			<a href="index.html" class="flex items-center gap-2 group" aria-label="Konspect home">
				<span class="brand-font text-2xl font-bold tracking-tight text-brand">Konspect</span>
			</a>
			<div class="flex items-center gap-3">
				<span class="text-sm text-muted">Already have an account?</span>
				<a href="login.html" class="btn inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Log In</a>
			</div>
		</nav>
	</header>

	<main class="min-h-screen bg-paper">
		<section class="px-6 py-12">
			<div class="max-w-4xl mx-auto">
				<!-- Header -->
				<header class="text-center mb-12">
					<h1 class="text-3xl sm:text-4xl font-serif font-bold text-ink">Create Your Account</h1>
					<p class="mt-3 text-lg text-muted">Join thousands of businesses accessing African market data</p>
				</header>

				<!-- Step Indicator -->
				<div class="flex items-center justify-center mb-12">
					<div class="flex items-center gap-4">
						<div class="step-indicator active flex items-center justify-center w-8 h-8 rounded-full bg-gray-200 text-sm font-semibold">1</div>
						<div class="w-12 h-0.5 bg-gray-200"></div>
						<div class="step-indicator flex items-center justify-center w-8 h-8 rounded-full bg-gray-200 text-sm font-semibold">2</div>
						<div class="w-12 h-0.5 bg-gray-200"></div>
						<div class="step-indicator flex items-center justify-center w-8 h-8 rounded-full bg-gray-200 text-sm font-semibold">3</div>
					</div>
				</div>

				<!-- Registration Form -->
				<article class="bg-white rounded-2xl border border-black/10 shadow-card p-8 lg:p-12">
					<form id="registrationForm" class="space-y-8" novalidate>
						<!-- Step 1: Personal Information -->
						<div id="step1" class="step-content">
							<h2 class="text-xl font-serif font-bold text-ink mb-6">Personal Information</h2>
							
							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								<!-- First Name -->
								<div>
									<label for="firstName" class="block text-sm font-semibold text-ink mb-2">First Name *</label>
									<input 
										type="text" 
										id="firstName" 
										name="firstName" 
										required 
										class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
										placeholder="John"
										autocomplete="given-name"
									>
									<div id="firstNameError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>

								<!-- Last Name -->
								<div>
									<label for="lastName" class="block text-sm font-semibold text-ink mb-2">Last Name *</label>
									<input 
										type="text" 
										id="lastName" 
										name="lastName" 
										required 
										class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
										placeholder="Doe"
										autocomplete="family-name"
									>
									<div id="lastNameError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>

								<!-- Business Email -->
								<div>
									<label for="email" class="block text-sm font-semibold text-ink mb-2">Business Email *</label>
									<input 
										type="email" 
										id="email" 
										name="email" 
										required 
										class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
										placeholder="<EMAIL>"
										autocomplete="email"
									>
									<div id="emailError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>

								<!-- Phone Number -->
								<div>
									<label for="phone" class="block text-sm font-semibold text-ink mb-2">Phone Number *</label>
									<div class="flex gap-2">
										<select
											id="countryCode"
											name="countryCode"
											class="form-select px-3 py-3 border border-black/20 rounded-lg text-ink focus:outline-none bg-white"
										>
											<option value="+1">🇺🇸 +1</option>
											<option value="+44">🇬🇧 +44</option>
											<option value="+33">🇫🇷 +33</option>
											<option value="+49">🇩🇪 +49</option>
											<option value="+228">🇹🇬 +228</option>
											<option value="+229">🇧🇯 +229</option>
											<option value="+225">🇨🇮 +225</option>
											<option value="+221">🇸🇳 +221</option>
											<option value="+226">🇧🇫 +226</option>
											<option value="+223">🇲🇱 +223</option>
											<option value="+227">🇳🇪 +227</option>
											<option value="+235">🇹🇩 +235</option>
											<option value="+236">🇨🇫 +236</option>
											<option value="+241">🇬🇦 +241</option>
											<option value="+240">🇬🇶 +240</option>
											<option value="+237">🇨🇲 +237</option>
										</select>
										<input 
											type="tel" 
											id="phone" 
											name="phone" 
											required 
											class="form-input flex-1 px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
											placeholder="************"
											autocomplete="tel"
										>
									</div>
									<div id="phoneError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>

								<!-- Password -->
								<div>
									<label for="password" class="block text-sm font-semibold text-ink mb-2">Password *</label>
									<div class="relative">
										<input 
											type="password" 
											id="password" 
											name="password" 
											required 
											class="form-input w-full px-4 py-3 pr-12 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
											placeholder="Create a strong password"
											autocomplete="new-password"
										>
										<button 
											type="button" 
											id="togglePassword" 
											class="password-toggle absolute right-3 top-1/2 -translate-y-1/2 text-muted hover:text-ink focus:outline-none focus:text-ink"
											aria-label="Toggle password visibility"
										>
											<i class="fa-solid fa-eye" id="eyeIcon"></i>
										</button>
									</div>
									<div id="passwordError" class="mt-1 text-sm text-red-600 hidden"></div>
									<div class="mt-1 text-xs text-muted">Must be at least 8 characters with uppercase, lowercase, and number</div>
								</div>

								<!-- Confirm Password -->
								<div>
									<label for="confirmPassword" class="block text-sm font-semibold text-ink mb-2">Confirm Password *</label>
									<input 
										type="password" 
										id="confirmPassword" 
										name="confirmPassword" 
										required 
										class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
										placeholder="Confirm your password"
										autocomplete="new-password"
									>
									<div id="confirmPasswordError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>
							</div>

							<div class="flex justify-end pt-6">
								<button 
									type="button" 
									id="nextStep1" 
									class="btn inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60"
								>
									Next: Address Information
									<i class="fa-solid fa-arrow-right ml-2"></i>
								</button>
							</div>
						</div>

						<!-- Step 2: Address Information -->
						<div id="step2" class="step-content hidden">
							<h2 class="text-xl font-serif font-bold text-ink mb-6">Address Information</h2>

							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								<!-- Street Address -->
								<div class="md:col-span-2">
									<label for="streetAddress" class="block text-sm font-semibold text-ink mb-2">Street Address *</label>
									<input
										type="text"
										id="streetAddress"
										name="streetAddress"
										required
										class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
										placeholder="123 Business Street"
										autocomplete="street-address"
									>
									<div id="streetAddressError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>

								<!-- City -->
								<div>
									<label for="city" class="block text-sm font-semibold text-ink mb-2">City *</label>
									<input
										type="text"
										id="city"
										name="city"
										required
										class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
										placeholder="New York"
										autocomplete="address-level2"
									>
									<div id="cityError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>

								<!-- State/Region -->
								<div>
									<label for="state" class="block text-sm font-semibold text-ink mb-2">State/Region *</label>
									<input
										type="text"
										id="state"
										name="state"
										required
										class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
										placeholder="NY"
										autocomplete="address-level1"
									>
									<div id="stateError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>

								<!-- Zip Code -->
								<div>
									<label for="zipCode" class="block text-sm font-semibold text-ink mb-2">Zip Code/Postal Code *</label>
									<input
										type="text"
										id="zipCode"
										name="zipCode"
										required
										class="form-input w-full px-4 py-3 border border-black/20 rounded-lg text-ink placeholder:text-muted/70 focus:outline-none"
										placeholder="10001"
										autocomplete="postal-code"
									>
									<div id="zipCodeError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>

								<!-- Country -->
								<div>
									<label for="country" class="block text-sm font-semibold text-ink mb-2">Country *</label>
									<select
										id="country"
										name="country"
										required
										class="form-select w-full px-4 py-3 border border-black/20 rounded-lg text-ink focus:outline-none bg-white"
										autocomplete="country"
									>
										<option value="">Select Country</option>
										<option value="US">United States</option>
										<option value="GB">United Kingdom</option>
										<option value="FR">France</option>
										<option value="DE">Germany</option>
										<option value="CA">Canada</option>
										<option value="AU">Australia</option>
										<optgroup label="OHADA Countries">
											<option value="TG">Togo</option>
											<option value="BJ">Benin</option>
											<option value="CI">Côte d'Ivoire</option>
											<option value="SN">Senegal</option>
											<option value="BF">Burkina Faso</option>
											<option value="ML">Mali</option>
											<option value="NE">Niger</option>
											<option value="TD">Chad</option>
											<option value="CF">Central African Republic</option>
											<option value="GA">Gabon</option>
											<option value="GQ">Equatorial Guinea</option>
											<option value="CM">Cameroon</option>
											<option value="CG">Republic of the Congo</option>
											<option value="KM">Comoros</option>
											<option value="GW">Guinea-Bissau</option>
											<option value="GN">Guinea</option>
										</optgroup>
									</select>
									<div id="countryError" class="mt-1 text-sm text-red-600 hidden"></div>
								</div>
							</div>

							<div class="flex justify-between pt-6">
								<button
									type="button"
									id="prevStep2"
									class="btn inline-flex items-center justify-center rounded-lg border border-black/20 px-6 py-3 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60"
								>
									<i class="fa-solid fa-arrow-left mr-2"></i>
									Back
								</button>
								<button
									type="button"
									id="nextStep2"
									class="btn inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60"
								>
									Next: Plan Selection
									<i class="fa-solid fa-arrow-right ml-2"></i>
								</button>
							</div>
						</div>

						<!-- Step 3: Plan Selection -->
						<div id="step3" class="step-content hidden">
							<h2 class="text-xl font-serif font-bold text-ink mb-6">Choose Your Plan</h2>

							<!-- Plan Selection -->
							<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
								<!-- Subscription Plan -->
								<div class="plan-card border-2 border-black/10 rounded-xl p-6 text-center" data-plan="subscription">
									<div class="mb-4">
										<h3 class="text-lg font-serif font-bold text-ink">Subscription Plan</h3>
										<p class="text-sm text-muted mt-1">Best for regular usage</p>
									</div>
									<div class="mb-4">
										<p class="text-3xl font-bold text-ink">$49.99<span class="text-base font-normal text-muted">/mo</span></p>
										<p class="text-sm text-muted">From 100 requests/month</p>
									</div>
									<div class="text-sm text-muted">
										<p>✓ Predictable monthly costs</p>
										<p>✓ Volume discounts available</p>
										<p>✓ Priority support</p>
									</div>
								</div>

								<!-- Pay As You Go -->
								<div class="plan-card border-2 border-black/10 rounded-xl p-6 text-center" data-plan="payg">
									<div class="mb-4">
										<h3 class="text-lg font-serif font-bold text-ink">Pay As You Go</h3>
										<p class="text-sm text-muted mt-1">Perfect for occasional use</p>
									</div>
									<div class="mb-4">
										<p class="text-3xl font-bold text-ink">$1.99<span class="text-base font-normal text-muted">/request</span></p>
										<p class="text-sm text-muted">No monthly commitment</p>
									</div>
									<div class="text-sm text-muted">
										<p>✓ No monthly fees</p>
										<p>✓ Pay only for what you use</p>
										<p>✓ Standard support</p>
									</div>
								</div>
							</div>

							<!-- Billing Cycle (only for subscription) -->
							<div id="billingCycleSection" class="mb-8 hidden">
								<h3 class="text-lg font-semibold text-ink mb-4">Billing Cycle</h3>
								<div class="flex items-center justify-center gap-4 p-4 bg-gray-50 rounded-lg">
									<span class="text-sm font-semibold text-ink" id="monthlyLabel">Monthly</span>
									<div class="billing-toggle" id="billingToggle" role="switch" aria-checked="false" aria-labelledby="billingLabel">
									</div>
									<span class="text-sm font-semibold text-ink" id="annualLabel">Annual</span>
									<span class="ml-2 px-2 py-1 bg-brand text-white text-xs font-semibold rounded">Save 20%</span>
								</div>
								<p class="text-center text-sm text-muted mt-2">
									<span id="billingPrice">$49.99/month</span> • Cancel anytime
								</p>
							</div>

							<!-- Legal Agreements -->
							<div class="space-y-4 mb-8">
								<h3 class="text-lg font-semibold text-ink">Legal Agreements</h3>

								<label class="flex items-start gap-3 cursor-pointer">
									<input type="checkbox" id="termsAccepted" name="termsAccepted" required class="mt-1 w-4 h-4 text-brand border-black/20 rounded focus:ring-brand/60">
									<span class="text-sm text-muted">
										I agree to the <a href="#" class="text-brand hover:underline">Terms of Use</a> *
									</span>
								</label>

								<label class="flex items-start gap-3 cursor-pointer">
									<input type="checkbox" id="privacyAccepted" name="privacyAccepted" required class="mt-1 w-4 h-4 text-brand border-black/20 rounded focus:ring-brand/60">
									<span class="text-sm text-muted">
										I agree to the <a href="#" class="text-brand hover:underline">Privacy Policy</a> *
									</span>
								</label>

								<label class="flex items-start gap-3 cursor-pointer">
									<input type="checkbox" id="usageAccepted" name="usageAccepted" required class="mt-1 w-4 h-4 text-brand border-black/20 rounded focus:ring-brand/60">
									<span class="text-sm text-muted">
										I agree to the <a href="#" class="text-brand hover:underline">Acceptable Usage Policy</a> *
									</span>
								</label>

								<label class="flex items-start gap-3 cursor-pointer">
									<input type="checkbox" id="newsletter" name="newsletter" class="mt-1 w-4 h-4 text-brand border-black/20 rounded focus:ring-brand/60">
									<span class="text-sm text-muted">
										Subscribe to our newsletter for product updates and industry insights
									</span>
								</label>
							</div>

							<div class="flex justify-between pt-6">
								<button
									type="button"
									id="prevStep3"
									class="btn inline-flex items-center justify-center rounded-lg border border-black/20 px-6 py-3 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60"
								>
									<i class="fa-solid fa-arrow-left mr-2"></i>
									Back
								</button>
								<button
									type="submit"
									id="submitRegistration"
									class="btn inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span id="submitText">Create Account</span>
									<i id="submitSpinner" class="fa-solid fa-spinner fa-spin ml-2 hidden"></i>
								</button>
							</div>
						</div>
					</form>
				</article>

				<!-- Login Link -->
				<p class="mt-8 text-center text-sm text-muted">
					Already have an account? 
					<a href="login.html" class="text-brand hover:underline focus:outline-none focus:underline font-semibold">Sign in</a>
				</p>
			</div>
		</section>
	</main>

	<!-- Scripts -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/js/all.min.js"></script>
	<script>
		// Global state
		let currentStep = 1;
		let selectedPlan = null;
		let isAnnualBilling = false;

		// Password visibility toggle
		(function(){
			const toggleBtn = document.getElementById('togglePassword');
			const passwordInput = document.getElementById('password');
			const eyeIcon = document.getElementById('eyeIcon');

			if(!toggleBtn || !passwordInput || !eyeIcon) return;

			toggleBtn.addEventListener('click', () => {
				const isPassword = passwordInput.type === 'password';
				passwordInput.type = isPassword ? 'text' : 'password';
				eyeIcon.className = isPassword ? 'fa-solid fa-eye-slash' : 'fa-solid fa-eye';
			});
		})();

		// Step navigation
		function showStep(step) {
			// Hide all steps
			document.querySelectorAll('.step-content').forEach(el => el.classList.add('hidden'));

			// Show current step
			document.getElementById(`step${step}`).classList.remove('hidden');

			// Update step indicators
			document.querySelectorAll('.step-indicator').forEach((el, index) => {
				el.classList.remove('active', 'completed');
				if (index + 1 < step) {
					el.classList.add('completed');
				} else if (index + 1 === step) {
					el.classList.add('active');
				}
			});

			currentStep = step;
		}

		// Validation functions
		function validateEmail(email) {
			const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			return re.test(email);
		}

		function validatePassword(password) {
			// At least 8 characters, 1 uppercase, 1 lowercase, 1 number
			const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
			return re.test(password);
		}

		function validatePhone(phone) {
			// Basic phone validation (digits, spaces, dashes, parentheses)
			const re = /^[\d\s\-\(\)]+$/;
			return re.test(phone) && phone.replace(/\D/g, '').length >= 10;
		}

		// Show/hide error messages
		function showError(input, message) {
			const errorEl = document.getElementById(input.name + 'Error');
			if(errorEl) {
				errorEl.textContent = message;
				errorEl.classList.remove('hidden');
			}
			input.classList.add('error');
		}

		function clearError(input) {
			const errorEl = document.getElementById(input.name + 'Error');
			if(errorEl) {
				errorEl.classList.add('hidden');
			}
			input.classList.remove('error');
		}

		// Step validation
		function validateStep1() {
			let isValid = true;
			const firstName = document.getElementById('firstName');
			const lastName = document.getElementById('lastName');
			const email = document.getElementById('email');
			const phone = document.getElementById('phone');
			const password = document.getElementById('password');
			const confirmPassword = document.getElementById('confirmPassword');

			// Clear previous errors
			[firstName, lastName, email, phone, password, confirmPassword].forEach(clearError);

			// Validate first name
			if (!firstName.value.trim()) {
				showError(firstName, 'First name is required');
				isValid = false;
			}

			// Validate last name
			if (!lastName.value.trim()) {
				showError(lastName, 'Last name is required');
				isValid = false;
			}

			// Validate email
			if (!email.value.trim()) {
				showError(email, 'Email is required');
				isValid = false;
			} else if (!validateEmail(email.value)) {
				showError(email, 'Please enter a valid email address');
				isValid = false;
			}

			// Validate phone
			if (!phone.value.trim()) {
				showError(phone, 'Phone number is required');
				isValid = false;
			} else if (!validatePhone(phone.value)) {
				showError(phone, 'Please enter a valid phone number');
				isValid = false;
			}

			// Validate password
			if (!password.value) {
				showError(password, 'Password is required');
				isValid = false;
			} else if (!validatePassword(password.value)) {
				showError(password, 'Password must be at least 8 characters with uppercase, lowercase, and number');
				isValid = false;
			}

			// Validate confirm password
			if (!confirmPassword.value) {
				showError(confirmPassword, 'Please confirm your password');
				isValid = false;
			} else if (password.value !== confirmPassword.value) {
				showError(confirmPassword, 'Passwords do not match');
				isValid = false;
			}

			return isValid;
		}

		function validateStep2() {
			let isValid = true;
			const streetAddress = document.getElementById('streetAddress');
			const city = document.getElementById('city');
			const state = document.getElementById('state');
			const zipCode = document.getElementById('zipCode');
			const country = document.getElementById('country');

			// Clear previous errors
			[streetAddress, city, state, zipCode, country].forEach(clearError);

			// Validate all required fields
			if (!streetAddress.value.trim()) {
				showError(streetAddress, 'Street address is required');
				isValid = false;
			}

			if (!city.value.trim()) {
				showError(city, 'City is required');
				isValid = false;
			}

			if (!state.value.trim()) {
				showError(state, 'State/Region is required');
				isValid = false;
			}

			if (!zipCode.value.trim()) {
				showError(zipCode, 'Zip code is required');
				isValid = false;
			}

			if (!country.value) {
				showError(country, 'Please select a country');
				isValid = false;
			}

			return isValid;
		}

		function validateStep3() {
			let isValid = true;

			// Check if plan is selected
			if (!selectedPlan) {
				alert('Please select a plan');
				isValid = false;
			}

			// Check required checkboxes
			const termsAccepted = document.getElementById('termsAccepted');
			const privacyAccepted = document.getElementById('privacyAccepted');
			const usageAccepted = document.getElementById('usageAccepted');

			if (!termsAccepted.checked) {
				alert('You must agree to the Terms of Use');
				isValid = false;
			}

			if (!privacyAccepted.checked) {
				alert('You must agree to the Privacy Policy');
				isValid = false;
			}

			if (!usageAccepted.checked) {
				alert('You must agree to the Acceptable Usage Policy');
				isValid = false;
			}

			return isValid;
		}

		// Plan selection
		function initializePlanSelection() {
			const planCards = document.querySelectorAll('.plan-card');
			const billingToggle = document.getElementById('billingToggle');
			const billingSection = document.getElementById('billingCycleSection');
			const billingPrice = document.getElementById('billingPrice');

			planCards.forEach(card => {
				card.addEventListener('click', () => {
					// Remove selection from all cards
					planCards.forEach(c => c.classList.remove('selected'));

					// Select clicked card
					card.classList.add('selected');
					selectedPlan = card.dataset.plan;

					// Show/hide billing cycle section
					if (selectedPlan === 'subscription') {
						billingSection.classList.remove('hidden');
					} else {
						billingSection.classList.add('hidden');
					}
				});
			});

			// Billing toggle
			billingToggle.addEventListener('click', () => {
				isAnnualBilling = !isAnnualBilling;
				billingToggle.classList.toggle('active', isAnnualBilling);
				billingToggle.setAttribute('aria-checked', isAnnualBilling);

				// Update pricing display
				if (isAnnualBilling) {
					billingPrice.textContent = '$39.99/month (billed annually)';
				} else {
					billingPrice.textContent = '$49.99/month';
				}
			});
		}

		// Event listeners
		document.addEventListener('DOMContentLoaded', () => {
			// Initialize plan selection
			initializePlanSelection();

			// Step navigation buttons
			document.getElementById('nextStep1').addEventListener('click', () => {
				if (validateStep1()) {
					showStep(2);
				}
			});

			document.getElementById('nextStep2').addEventListener('click', () => {
				if (validateStep2()) {
					showStep(3);
				}
			});

			document.getElementById('prevStep2').addEventListener('click', () => {
				showStep(1);
			});

			document.getElementById('prevStep3').addEventListener('click', () => {
				showStep(2);
			});

			// Real-time validation
			const emailInput = document.getElementById('email');
			const passwordInput = document.getElementById('password');
			const confirmPasswordInput = document.getElementById('confirmPassword');

			emailInput.addEventListener('blur', () => {
				if (emailInput.value && !validateEmail(emailInput.value)) {
					showError(emailInput, 'Please enter a valid email address');
				} else {
					clearError(emailInput);
				}
			});

			passwordInput.addEventListener('input', () => {
				if (passwordInput.classList.contains('error')) {
					clearError(passwordInput);
				}
				// Also clear confirm password error if passwords now match
				if (confirmPasswordInput.value && passwordInput.value === confirmPasswordInput.value) {
					clearError(confirmPasswordInput);
				}
			});

			confirmPasswordInput.addEventListener('input', () => {
				if (confirmPasswordInput.classList.contains('error')) {
					clearError(confirmPasswordInput);
				}
			});

			// Form submission
			document.getElementById('registrationForm').addEventListener('submit', async (e) => {
				e.preventDefault();

				if (!validateStep3()) return;

				const submitBtn = document.getElementById('submitRegistration');
				const submitText = document.getElementById('submitText');
				const submitSpinner = document.getElementById('submitSpinner');

				// Show loading state
				submitBtn.disabled = true;
				submitText.textContent = 'Creating Account...';
				submitSpinner.classList.remove('hidden');

				// Collect form data
				const formData = new FormData(e.target);
				const data = Object.fromEntries(formData.entries());
				data.selectedPlan = selectedPlan;
				data.isAnnualBilling = isAnnualBilling;

				try {
					// Simulate API call
					await new Promise(resolve => setTimeout(resolve, 3000));

					// Success - redirect or show success message
					alert('Account created successfully! Welcome to Konspect! (This is a demo)');

					// In a real app, you would redirect to dashboard or login
					// window.location.href = '/dashboard';

				} catch (error) {
					alert('Registration failed. Please try again.');
				} finally {
					// Reset button state
					submitBtn.disabled = false;
					submitText.textContent = 'Create Account';
					submitSpinner.classList.add('hidden');
				}
			});
		});
	</script>
