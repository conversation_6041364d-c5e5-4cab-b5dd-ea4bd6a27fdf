<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Contact Us — Konspect</title>
	<meta name="description" content="Get in touch with Konspect. Contact our team for support, partnerships, or questions about our African business verification API.">

	<!-- Google Fonts: <PERSON><PERSON> (brand), <PERSON><PERSON> (body), <PERSON>rri<PERSON>ather (headings) -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link
		href="https://fonts.googleapis.com/css2?family=Fredoka:wght@600;700&family=Lato:wght@300;400;700;900&family=Merriweather:wght@700;900&display=swap"
		rel="stylesheet">

	<!-- Font Awesome (for icons) -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">

	<!-- Tailwind CSS CDN -->
	<script src="https://cdn.tailwindcss.com"></script>
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						brand: '#FF6039',
						ink: '#1D1D1D',
						muted: '#585D62',
						paper: '#FAFAFA'
					},
					fontFamily: {
						sans: ['Lato', 'ui-sans-serif', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
						serif: ['Merriweather', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
						fredoka: ['Fredoka', 'system-ui', 'sans-serif']
					}
				}
			}
		}
	</script>

	<style>
		:root {
			--brand: #FF6039;
			--ink: #1D1D1D;
			--muted: #585D62;
			--paper: #FAFAFA;
		}

		html {
			scroll-behavior: smooth;
		}

		body {
			font-family: Lato, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif;
			color: var(--muted);
			background-color: var(--paper);
		}

		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			font-family: Merriweather, Georgia, Cambria, Times New Roman, Times, serif;
			color: var(--ink);
		}

		.brand-font {
			font-family: Fredoka, system-ui, sans-serif;
		}

		.shadow-card {
			box-shadow: 0 16px 40px rgba(16, 24, 40, .08), 0 4px 10px rgba(16, 24, 40, .06);
		}

		.btn {
			transition: transform .08s ease, box-shadow .2s ease;
		}

		.btn:active {
			transform: translateY(1px);
		}

		.btn:disabled {
			opacity: 0.6;
			cursor: not-allowed;
			transform: none;
		}

		.contact-card {
			transition: transform 0.2s ease, box-shadow 0.2s ease;
		}

		.contact-card:hover {
			transform: translateY(-2px);
			box-shadow: 0 20px 50px rgba(16, 24, 40, .12), 0 8px 20px rgba(16, 24, 40, .08);
		}

		.form-field {
			transition: border-color 0.2s ease, box-shadow 0.2s ease;
		}

		.form-field:focus {
			border-color: var(--brand);
			box-shadow: 0 0 0 3px rgba(255, 96, 57, 0.1);
		}

		.field-error {
			border-color: #ef4444;
		}

		.field-success {
			border-color: #10b981;
		}

		.loading-spinner {
			border: 2px solid #f3f3f3;
			border-top: 2px solid var(--brand);
			border-radius: 50%;
			width: 20px;
			height: 20px;
			animation: spin 1s linear infinite;
		}

		@keyframes spin {
			0% { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}

		.fade-in {
			animation: fadeIn 0.5s ease-in-out;
		}

		@keyframes fadeIn {
			from { opacity: 0; transform: translateY(20px); }
			to { opacity: 1; transform: translateY(0); }
		}
	</style>
</head>

<body class="bg-white antialiased">
	<!-- Navbar -->
	<header
		class="border-b border-black/5 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 sticky top-0 z-50">
		<nav class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between" aria-label="Primary">
			<a href="index.html" class="flex items-center gap-2 group" aria-label="Konspect home">
				<span class="brand-font text-2xl font-bold tracking-tight text-brand">Konspect</span>
			</a>
			<ul class="hidden md:flex items-center gap-8 text-sm text-muted">
				<li><a href="pricing.html"
						class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Pricing</a>
				</li>
				<li><a href="index.html#docs"
						class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Documentation</a>
				</li>
				<li><a href="contact.html"
						class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1 text-brand font-semibold">Contact
						Us</a></li>
			</ul>
			<div class="hidden md:flex items-center gap-3">
				<a href="login.html"
					class="btn inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Log
					In</a>
				<a href="register.html"
					class="btn inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign
					Up</a>
			</div>
			<button id="mobileMenuBtn"
				class="md:hidden inline-flex h-10 w-10 items-center justify-center rounded-lg border border-black/10 text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60"
				aria-controls="mobileMenu" aria-expanded="false" aria-label="Open menu">
				<i class="fa-solid fa-bars"></i>
			</button>
		</nav>
		<div id="mobileMenu" class="md:hidden hidden border-t border-black/5">
			<div class="max-w-7xl mx-auto px-6 py-4 space-y-3">
				<a href="pricing.html" class="block">Pricing</a>
				<a href="index.html#docs" class="block">Documentation</a>
				<a href="contact.html" class="block text-brand font-semibold">Contact Us</a>
				<div class="pt-3 flex gap-3">
					<a href="login.html"
						class="btn flex-1 inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink">Log
						In</a>
					<a href="register.html"
						class="btn flex-1 inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white">Sign
						Up</a>
				</div>
			</div>
		</div>
	</header>

	<main>
		<!-- Hero Section -->
		<section class="relative bg-paper">
			<div class="max-w-7xl mx-auto px-6 py-12 md:py-16 lg:py-20 text-center">
				<h1 class="text-4xl sm:text-5xl xl:text-6xl font-black leading-tight tracking-tight text-ink">
					Get in <span class="text-brand">Touch</span>
				</h1>
				<p class="mt-6 max-w-3xl mx-auto text-lg text-muted">
					Have questions about our African business verification API? Need support or want to discuss a partnership?
					We're here to help you succeed.
				</p>
			</div>
		</section>

		<!-- Contact Methods -->
		<section aria-labelledby="contact-methods-title" class="py-14 sm:py-18 lg:py-24 bg-white">
			<div class="max-w-7xl mx-auto px-6">
				<h2 id="contact-methods-title" class="text-center font-serif text-3xl sm:text-4xl font-black text-ink mb-12">
					How Can We <span class="text-brand">Help?</span>
				</h2>

				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
					<!-- General Support -->
					<article class="contact-card rounded-2xl border border-black/10 bg-white p-8 text-center shadow-card">
						<div class="inline-flex h-16 w-16 items-center justify-center rounded-full bg-brand/10 text-brand text-2xl mb-6">
							<i class="fa-solid fa-headset"></i>
						</div>
						<h3 class="text-xl font-serif font-bold text-ink mb-4">General Support</h3>
						<p class="text-muted mb-6">
							Questions about our API, pricing, or technical issues? Our support team is ready to assist.
						</p>
						<div class="space-y-2 text-sm">
							<p class="flex items-center justify-center gap-2">
								<i class="fa-solid fa-envelope text-brand"></i>
								<a href="mailto:<EMAIL>" class="text-brand hover:underline"><EMAIL></a>
							</p>
							<p class="flex items-center justify-center gap-2">
								<i class="fa-solid fa-clock text-brand"></i>
								<span>Mon-Fri, 8AM-6PM UTC</span>
							</p>
						</div>
					</article>

					<!-- Sales & Partnerships -->
					<article class="contact-card rounded-2xl border border-black/10 bg-white p-8 text-center shadow-card">
						<div class="inline-flex h-16 w-16 items-center justify-center rounded-full bg-brand/10 text-brand text-2xl mb-6">
							<i class="fa-solid fa-handshake"></i>
						</div>
						<h3 class="text-xl font-serif font-bold text-ink mb-4">Sales & Partnerships</h3>
						<p class="text-muted mb-6">
							Interested in enterprise solutions, bulk pricing, or strategic partnerships? Let's talk business.
						</p>
						<div class="space-y-2 text-sm">
							<p class="flex items-center justify-center gap-2">
								<i class="fa-solid fa-envelope text-brand"></i>
								<a href="mailto:<EMAIL>" class="text-brand hover:underline"><EMAIL></a>
							</p>
							<p class="flex items-center justify-center gap-2">
								<i class="fa-solid fa-phone text-brand"></i>
								<a href="tel:+***********" class="text-brand hover:underline">+228 90 12 34 56</a>
							</p>
						</div>
					</article>

					<!-- Technical Integration -->
					<article class="contact-card rounded-2xl border border-black/10 bg-white p-8 text-center shadow-card">
						<div class="inline-flex h-16 w-16 items-center justify-center rounded-full bg-brand/10 text-brand text-2xl mb-6">
							<i class="fa-solid fa-code"></i>
						</div>
						<h3 class="text-xl font-serif font-bold text-ink mb-4">Technical Integration</h3>
						<p class="text-muted mb-6">
							Need help integrating our API? Our technical team provides guidance and custom solutions.
						</p>
						<div class="space-y-2 text-sm">
							<p class="flex items-center justify-center gap-2">
								<i class="fa-solid fa-envelope text-brand"></i>
								<a href="mailto:<EMAIL>" class="text-brand hover:underline"><EMAIL></a>
							</p>
							<p class="flex items-center justify-center gap-2">
								<i class="fa-solid fa-book text-brand"></i>
								<a href="index.html#docs" class="text-brand hover:underline">API Documentation</a>
							</p>
						</div>
					</article>
				</div>
			</div>
		</section>

		<!-- Contact Form -->
		<section aria-labelledby="contact-form-title" class="py-14 sm:py-18 lg:py-24 bg-paper">
			<div class="max-w-4xl mx-auto px-6">
				<div class="text-center mb-12">
					<h2 id="contact-form-title" class="font-serif text-3xl sm:text-4xl font-black text-ink mb-6">
						Send Us a <span class="text-brand">Message</span>
					</h2>
					<p class="text-lg text-muted max-w-2xl mx-auto">
						Fill out the form below and we'll get back to you within 24 hours. For urgent matters, please call us directly.
					</p>
				</div>

				<div class="bg-white rounded-2xl border border-black/10 shadow-card p-8 md:p-12">
					<form id="contactForm" class="space-y-6" novalidate>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label for="firstName" class="block text-sm font-medium text-ink mb-2">
									First Name <span class="text-red-500">*</span>
								</label>
								<input type="text" id="firstName" name="firstName" required
									class="form-field w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:outline-none"
									placeholder="Enter your first name">
								<div class="error-message hidden text-red-500 text-sm mt-1"></div>
							</div>
							<div>
								<label for="lastName" class="block text-sm font-medium text-ink mb-2">
									Last Name <span class="text-red-500">*</span>
								</label>
								<input type="text" id="lastName" name="lastName" required
									class="form-field w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:outline-none"
									placeholder="Enter your last name">
								<div class="error-message hidden text-red-500 text-sm mt-1"></div>
							</div>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label for="email" class="block text-sm font-medium text-ink mb-2">
									Email Address <span class="text-red-500">*</span>
								</label>
								<input type="email" id="email" name="email" required
									class="form-field w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:outline-none"
									placeholder="<EMAIL>">
								<div class="error-message hidden text-red-500 text-sm mt-1"></div>
							</div>
							<div>
								<label for="phone" class="block text-sm font-medium text-ink mb-2">
									Phone Number
								</label>
								<input type="tel" id="phone" name="phone"
									class="form-field w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:outline-none"
									placeholder="+228 90 12 34 56">
								<div class="error-message hidden text-red-500 text-sm mt-1"></div>
							</div>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label for="company" class="block text-sm font-medium text-ink mb-2">
									Company Name
								</label>
								<input type="text" id="company" name="company"
									class="form-field w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:outline-none"
									placeholder="Your company name">
								<div class="error-message hidden text-red-500 text-sm mt-1"></div>
							</div>
							<div>
								<label for="subject" class="block text-sm font-medium text-ink mb-2">
									Subject <span class="text-red-500">*</span>
								</label>
								<select id="subject" name="subject" required
									class="form-field w-full rounded-lg border border-black/20 px-4 py-3 text-ink focus:outline-none">
									<option value="">Select a subject</option>
									<option value="general">General Inquiry</option>
									<option value="support">Technical Support</option>
									<option value="sales">Sales & Pricing</option>
									<option value="partnership">Partnership Opportunity</option>
									<option value="integration">API Integration Help</option>
									<option value="billing">Billing Question</option>
									<option value="other">Other</option>
								</select>
								<div class="error-message hidden text-red-500 text-sm mt-1"></div>
							</div>
						</div>

						<div>
							<label for="message" class="block text-sm font-medium text-ink mb-2">
								Message <span class="text-red-500">*</span>
							</label>
							<textarea id="message" name="message" rows="6" required
								class="form-field w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:outline-none resize-vertical"
								placeholder="Tell us how we can help you..."></textarea>
							<div class="error-message hidden text-red-500 text-sm mt-1"></div>
						</div>

						<div class="flex items-start gap-3">
							<input type="checkbox" id="privacy" name="privacy" required
								class="mt-1 h-4 w-4 rounded border-black/20 text-brand focus:ring-brand/60">
							<label for="privacy" class="text-sm text-muted">
								I agree to the <a href="#" class="text-brand hover:underline">Privacy Policy</a> and
								<a href="#" class="text-brand hover:underline">Terms of Service</a> <span class="text-red-500">*</span>
							</label>
						</div>

						<div class="flex items-center gap-4">
							<button type="submit" id="submitBtn"
								class="btn inline-flex items-center justify-center rounded-lg bg-brand px-8 py-3 text-base font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">
								<span id="submitText">Send Message</span>
								<div id="submitSpinner" class="loading-spinner ml-2 hidden"></div>
							</button>
							<div id="formStatus" class="text-sm hidden"></div>
						</div>
					</form>
				</div>
			</div>
		</section>

	<!-- Office Locations -->
	<section aria-labelledby="locations-title" class="py-14 sm:py-18 lg:py-24 bg-white">
		<div class="max-w-7xl mx-auto px-6">
			<h2 id="locations-title" class="text-center font-serif text-3xl sm:text-4xl font-black text-ink mb-12">
				Our <span class="text-brand">Locations</span>
			</h2>

			<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
				<!-- Head Office -->
				<article class="contact-card rounded-2xl border border-black/10 bg-white p-8 shadow-card">
					<div class="flex items-start gap-4">
						<div class="inline-flex h-12 w-12 items-center justify-center rounded-full bg-brand/10 text-brand text-xl flex-shrink-0">
							<i class="fa-solid fa-building"></i>
						</div>
						<div class="flex-1">
							<h3 class="text-xl font-serif font-bold text-ink mb-4">Head Office</h3>
							<address class="not-italic text-muted space-y-2">
								<p class="flex items-start gap-2">
									<i class="fa-solid fa-map-marker-alt text-brand mt-1 flex-shrink-0"></i>
									<span>14 Rue adjinamoto<br>Qt Avenou<br>Lomé, Togo<br>05 BP: 986</span>
								</p>
								<p class="flex items-center gap-2">
									<i class="fa-solid fa-phone text-brand"></i>
									<a href="tel:+***********" class="text-brand hover:underline">+228 90 12 34 56</a>
								</p>
								<p class="flex items-center gap-2">
									<i class="fa-solid fa-envelope text-brand"></i>
									<a href="mailto:<EMAIL>" class="text-brand hover:underline"><EMAIL></a>
								</p>
							</address>
							<div class="mt-6">
								<h4 class="font-semibold text-ink mb-2">Business Hours</h4>
								<div class="text-sm text-muted space-y-1">
									<p>Monday - Friday: 8:00 AM - 6:00 PM UTC</p>
									<p>Saturday: 9:00 AM - 2:00 PM UTC</p>
									<p>Sunday: Closed</p>
								</div>
							</div>
						</div>
					</div>
				</article>

				<!-- Map Placeholder -->
				<div class="contact-card rounded-2xl border border-black/10 bg-paper p-8 shadow-card">
					<div class="aspect-video bg-gradient-to-br from-brand/10 to-brand/5 rounded-xl flex items-center justify-center">
						<div class="text-center">
							<i class="fa-solid fa-map text-4xl text-brand mb-4"></i>
							<h3 class="text-xl font-serif font-bold text-ink mb-2">Find Us</h3>
							<p class="text-muted mb-4">Located in the heart of Lomé, Togo</p>
							<a href="https://maps.google.com/?q=14+Rue+adjinamoto+Qt+Avenou+Lome+Togo"
								target="_blank" rel="noopener noreferrer"
								class="btn inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">
								<i class="fa-solid fa-external-link-alt mr-2"></i>
								Open in Maps
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
		<!-- FAQ Section -->
		<section aria-labelledby="contact-faq-title" class="py-14 sm:py-18 lg:py-24 bg-paper">
			<div class="max-w-4xl mx-auto px-6">
				<h2 id="contact-faq-title" class="text-center font-serif text-3xl sm:text-4xl font-black text-ink mb-12">
					Frequently Asked <span class="text-brand">Questions</span>
				</h2>

				<div class="space-y-6">
					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>How quickly do you respond to support requests?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>We typically respond to support requests within 4-6 hours during business hours (Mon-Fri, 8AM-6PM UTC).
							For urgent technical issues, we aim to respond within 2 hours. Enterprise customers receive priority support
							with faster response times.</p>
						</div>
					</details>

					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>Do you offer phone support?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>Yes, we offer phone support for sales inquiries and enterprise customers. For technical support,
							we primarily use email and our support portal to ensure we can provide detailed assistance and maintain
							a record of our communications.</p>
						</div>
					</details>

					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>Can I schedule a demo or consultation?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>Absolutely! We offer personalized demos and consultations for potential enterprise customers.
							Contact our sales <NAME_EMAIL> or use the form above to schedule a meeting that works
							for your timezone.</p>
						</div>
					</details>

					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>What languages do you support?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>Our primary support language is English, but we also provide support in French given our location
							in Togo and our focus on African markets. Our API documentation is available in both languages.</p>
						</div>
					</details>
				</div>
			</div>
		</section>
	</main>
	<!-- Footer -->
	<footer class="bg-brand text-paper">
		<div class="max-w-7xl mx-auto px-6 pt-12 pb-10">
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
				<section aria-labelledby="office-title">
					<h3 id="office-title" class="font-semibold text-paper">Head Office</h3>
					<ul class="mt-4 space-y-1 text-sm/6 text-white/90">
						<li>14 Rue adjinamoto</li>
						<li>Qt Avenou</li>
						<li>Lome</li>
						<li>Togo</li>
						<li>05 BP: 986</li>
					</ul>
				</section>

				<section aria-labelledby="policies-title">
					<h3 id="policies-title" class="font-semibold text-paper">Policies and Documentation</h3>
					<ul class="mt-4 grid grid-cols-2 gap-x-6 gap-y-1 text-sm/6 text-white/90">
						<li><a href="#" class="hover:underline">Cookie policy</a></li>
						<li><a href="#" class="hover:underline">Information Security</a></li>
						<li><a href="#" class="hover:underline">Modern Slavery Act</a></li>
						<li><a href="#" class="hover:underline">Privacy Policy</a></li>
						<li><a href="#" class="hover:underline">Quality Policy</a></li>
						<li><a href="#" class="hover:underline">Terms and Conditions</a></li>
					</ul>
				</section>

				<section aria-labelledby="social-title">
					<h3 id="social-title" class="font-semibold text-paper">Social Media</h3>
					<div class="mt-4 flex items-center gap-4 text-lg">
						<a aria-label="Facebook" href="#"
							class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i
								class="fab fa-facebook-f"></i></a>
						<a aria-label="Instagram" href="#"
							class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i
								class="fab fa-instagram"></i></a>
						<a aria-label="WhatsApp" href="#"
							class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i
								class="fab fa-whatsapp"></i></a>
						<a aria-label="YouTube" href="#"
							class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i
								class="fab fa-youtube"></i></a>
					</div>
				</section>

				<section aria-labelledby="newsletter-title">
					<h3 id="newsletter-title" class="font-semibold text-paper">Subscribe to Newsletter</h3>
					<form class="mt-4 flex gap-3" action="#" method="get">
						<label for="newsletter-email" class="sr-only">Email Address</label>
						<input id="newsletter-email" type="email" required placeholder="Email Address"
							class="w-full rounded-lg border-0 px-4 py-2.5 text-ink placeholder:text-muted/70 focus:ring-2 focus:ring-brand/60">
						<button type="submit"
							class="btn rounded-lg bg-white px-4 py-2.5 text-sm font-semibold text-brand hover:bg-white/90">Subscribe</button>
					</form>
				</section>
			</div>

			<div class="mt-12 border-t border-white/20 pt-6 text-xs text-white/80">
				<p>&copy; 2023 Konspect Sarl. Registered in Togo, NIF: XXXXXXXX, RCCM: </p>
			</div>
		</div>
	</footer>
	<!-- Scripts: tiny interactivity only -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/js/all.min.js"></script>
	<script>
		// Mobile menu toggle
		(function () {
			const btn = document.getElementById('mobileMenuBtn');
			const menu = document.getElementById('mobileMenu');
			if (!btn || !menu) return;
			btn.addEventListener('click', () => {
				const expanded = btn.getAttribute('aria-expanded') === 'true';
				btn.setAttribute('aria-expanded', String(!expanded));
				menu.classList.toggle('hidden');
			});
		})();

		// Contact form functionality
		(function () {
			const form = document.getElementById('contactForm');
			const submitBtn = document.getElementById('submitBtn');
			const submitText = document.getElementById('submitText');
			const submitSpinner = document.getElementById('submitSpinner');
			const formStatus = document.getElementById('formStatus');

			if (!form) return;

			// Form validation
			function validateField(field) {
				const value = field.value.trim();
				const errorDiv = field.parentNode.querySelector('.error-message');
				let isValid = true;
				let errorMessage = '';

				// Remove previous validation classes
				field.classList.remove('field-error', 'field-success');

				if (field.hasAttribute('required') && !value) {
					isValid = false;
					errorMessage = 'This field is required.';
				} else if (field.type === 'email' && value) {
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(value)) {
						isValid = false;
						errorMessage = 'Please enter a valid email address.';
					}
				} else if (field.type === 'tel' && value) {
					const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
					if (!phoneRegex.test(value)) {
						isValid = false;
						errorMessage = 'Please enter a valid phone number.';
					}
				}

				// Show/hide error message
				if (errorMessage) {
					errorDiv.textContent = errorMessage;
					errorDiv.classList.remove('hidden');
					field.classList.add('field-error');
				} else {
					errorDiv.classList.add('hidden');
					if (value) field.classList.add('field-success');
				}

				return isValid;
			}

			// Real-time validation
			const formFields = form.querySelectorAll('input, select, textarea');
			formFields.forEach(field => {
				field.addEventListener('blur', () => validateField(field));
				field.addEventListener('input', () => {
					if (field.classList.contains('field-error')) {
						validateField(field);
					}
				});
			});

			// Form submission
			form.addEventListener('submit', async (e) => {
				e.preventDefault();

				// Validate all fields
				let isFormValid = true;
				formFields.forEach(field => {
					if (!validateField(field)) {
						isFormValid = false;
					}
				});

				// Check privacy checkbox
				const privacyCheckbox = document.getElementById('privacy');
				if (!privacyCheckbox.checked) {
					isFormValid = false;
					formStatus.textContent = 'Please accept the Privacy Policy and Terms of Service.';
					formStatus.className = 'text-sm text-red-500';
					formStatus.classList.remove('hidden');
				}

				if (!isFormValid) {
					// Scroll to first error
					const firstError = form.querySelector('.field-error');
					if (firstError) {
						firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
						firstError.focus();
					}
					return;
				}

				// Show loading state
				submitBtn.disabled = true;
				submitText.textContent = 'Sending...';
				submitSpinner.classList.remove('hidden');
				formStatus.classList.add('hidden');

				// Simulate form submission (replace with actual API call)
				try {
					await new Promise(resolve => setTimeout(resolve, 2000));

					// Success state
					formStatus.textContent = 'Thank you! Your message has been sent successfully. We\'ll get back to you within 24 hours.';
					formStatus.className = 'text-sm text-green-600 fade-in';
					formStatus.classList.remove('hidden');

					// Reset form
					form.reset();
					formFields.forEach(field => {
						field.classList.remove('field-error', 'field-success');
						const errorDiv = field.parentNode.querySelector('.error-message');
						if (errorDiv) errorDiv.classList.add('hidden');
					});

				} catch (error) {
					// Error state
					formStatus.textContent = 'Sorry, there was an error sending your message. Please try again or contact us directly.';
					formStatus.className = 'text-sm text-red-500';
					formStatus.classList.remove('hidden');
				} finally {
					// Reset button state
					submitBtn.disabled = false;
					submitText.textContent = 'Send Message';
					submitSpinner.classList.add('hidden');
				}
			});
		})();
	</script>
</body>

</html>
