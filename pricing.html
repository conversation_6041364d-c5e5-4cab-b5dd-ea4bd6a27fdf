<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Pricing Plans — Konspect</title>

	<!-- Google Fonts: <PERSON><PERSON> (brand), <PERSON><PERSON> (body), <PERSON><PERSON><PERSON><PERSON> (headings) -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link
		href="https://fonts.googleapis.com/css2?family=Fredoka:wght@600;700&family=Lato:wght@300;400;700;900&family=Merriweather:wght@700;900&display=swap"
		rel="stylesheet">

	<!-- Font Awesome (for icons) -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">

	<!-- Tailwind CSS CDN -->
	<script src="https://cdn.tailwindcss.com"></script>
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						brand: '#FF6039',
						ink: '#1D1D1D',
						muted: '#585D62',
						paper: '#FAFAFA'
					},
					fontFamily: {
						sans: ['Lato', 'ui-sans-serif', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
						serif: ['Merriweather', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
						fredoka: ['Fredoka', 'system-ui', 'sans-serif']
					}
				}
			}
		}
	</script>

	<style>
		:root {
			--brand: #FF6039;
			--ink: #1D1D1D;
			--muted: #585D62;
			--paper: #FAFAFA;
		}

		html {
			scroll-behavior: smooth;
		}

		body {
			font-family: Lato, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif;
			color: var(--muted);
			background-color: var(--paper);
		}

		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			font-family: Merriweather, Georgia, Cambria, Times New Roman, Times, serif;
			color: var(--ink);
		}

		.brand-font {
			font-family: Fredoka, system-ui, sans-serif;
		}

		.shadow-card {
			box-shadow: 0 16px 40px rgba(16, 24, 40, .08), 0 4px 10px rgba(16, 24, 40, .06);
		}

		.btn {
			transition: transform .08s ease, box-shadow .2s ease;
		}

		.btn:active {
			transform: translateY(1px);
		}

		.pricing-card {
			transition: transform 0.2s ease, box-shadow 0.2s ease;
		}

		.pricing-card:hover {
			transform: translateY(-4px);
			box-shadow: 0 20px 50px rgba(16, 24, 40, .12), 0 8px 20px rgba(16, 24, 40, .08);
		}

		.feature-check {
			color: var(--brand);
		}

		.billing-toggle {
			transition: all 0.2s ease;
		}
	</style>
</head>

<body class="bg-white antialiased">
	<!-- Navbar -->
	<header
		class="border-b border-black/5 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 sticky top-0 z-50">
		<nav class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between" aria-label="Primary">
			<a href="index.html" class="flex items-center gap-2 group" aria-label="Konspect home">
				<span class="brand-font text-2xl font-bold tracking-tight text-brand">Konspect</span>
			</a>
			<ul class="hidden md:flex items-center gap-8 text-sm text-muted">
				<li><a href="pricing.html"
						class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1 text-brand font-semibold">Pricing</a>
				</li>
				<li><a href="index.html#docs"
						class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Documentation</a>
				</li>
				<li><a href="index.html#contact"
						class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Contact
						Us</a></li>
			</ul>
			<div class="hidden md:flex items-center gap-3">
				<a href="login.html"
					class="btn inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Log
					In</a>
				<a href="register.html"
					class="btn inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign
					Up</a>
			</div>
			<button id="mobileMenuBtn"
				class="md:hidden inline-flex h-10 w-10 items-center justify-center rounded-lg border border-black/10 text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60"
				aria-controls="mobileMenu" aria-expanded="false" aria-label="Open menu">
				<i class="fa-solid fa-bars"></i>
			</button>
		</nav>
		<div id="mobileMenu" class="md:hidden hidden border-t border-black/5">
			<div class="max-w-7xl mx-auto px-6 py-4 space-y-3">
				<a href="pricing.html" class="block text-brand font-semibold">Pricing</a>
				<a href="index.html#docs" class="block">Documentation</a>
				<a href="index.html#contact" class="block">Contact Us</a>
				<div class="pt-3 flex gap-3">
					<a href="login.html"
						class="btn flex-1 inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink">Log
						In</a>
					<a href="register.html"
						class="btn flex-1 inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white">Sign
						Up</a>
				</div>
			</div>
		</div>
	</header>

	<main>
		<!-- Hero Section -->
		<section class="relative bg-paper">
			<div class="max-w-7xl mx-auto px-6 py-12 md:py-16 lg:py-20 text-center">
				<h1 class="text-4xl sm:text-5xl xl:text-6xl font-black leading-tight tracking-tight text-ink">
					Simple, Transparent<br class="hidden sm:block"> <span class="text-brand">Pricing</span>
				</h1>
				<p class="mt-6 max-w-3xl mx-auto text-lg text-muted">
					Choose the perfect plan for your business verification needs. Access 9.7M+ African businesses with
					our powerful REST API.
				</p>

				<!-- Billing Toggle -->
				<div class="mt-8 flex items-center justify-center gap-4">
					<span class="text-sm font-medium text-muted" id="monthlyLabel">Monthly</span>
					<button id="billingToggle"
						class="billing-toggle relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60"
						role="switch" aria-checked="false" aria-labelledby="monthlyLabel yearlyLabel">
						<span
							class="inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform translate-x-1"></span>
					</button>
					<span class="text-sm font-medium text-muted" id="yearlyLabel">Yearly <span
							class="text-brand font-semibold">(Save 20%)</span></span>
				</div>
			</div>
		</section>

		<!-- Pricing Plans -->
		<section aria-labelledby="pricing-title" class="py-14 sm:py-18 lg:py-24 bg-white">
			<div class="max-w-7xl mx-auto px-6">
				<h2 id="pricing-title" class="sr-only">Pricing Plans</h2>

				<div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
					<!-- Subscription Plan -->
					<article class="pricing-card rounded-2xl border-2 border-brand bg-white p-8 shadow-card relative">
						<div class="absolute -top-4 left-1/2 -translate-x-1/2">
							<span class="bg-brand text-white text-xs font-semibold px-3 py-1 rounded-full">Most Popular</span>
						</div>
						<div class="text-center">
							<h3 class="text-xl font-serif font-bold text-ink">Subscription Plan</h3>
							<p class="mt-2 text-sm text-muted">Best for regular usage</p>
							<div class="mt-6">
								<span class="text-4xl font-extrabold tracking-tight text-ink" data-monthly="$49.99" data-yearly="$39.99">$49.99</span>
								<span class="text-base font-normal text-muted">/ month</span>
							</div>
							<p class="mt-2 text-sm text-muted">
								<span data-monthly="100" data-yearly="1,200">100</span> requests included
							</p>
							<p class="mt-1 text-xs text-muted">
								Additional requests: $0.99 each
							</p>
						</div>

						<ul class="mt-8 space-y-3 text-sm">
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>REST API Access</span>
							</li>
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>Web Form Verification</span>
							</li>
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>Predictable monthly costs</span>
							</li>
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>Priority Support</span>
							</li>
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>HTTPS Encryption</span>
							</li>
						</ul>

						<div class="mt-8">
							<a href="register.html" class="btn w-full inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up</a>
						</div>
					</article>

					<!-- Pay As You Go Plan -->
					<article class="pricing-card rounded-2xl border border-black/10 bg-white p-8 shadow-card">
						<div class="text-center">
							<h3 class="text-xl font-serif font-bold text-ink">Pay As You Go</h3>
							<p class="mt-2 text-sm text-muted">Perfect for occasional use</p>
							<div class="mt-6">
								<span class="text-4xl font-extrabold tracking-tight text-ink">$1.99</span>
								<span class="text-base font-normal text-muted">/ request</span>
							</div>
							<p class="mt-2 text-sm text-muted">
								No monthly commitment
							</p>
						</div>

						<ul class="mt-8 space-y-3 text-sm">
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>REST API Access</span>
							</li>
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>Web Form Verification</span>
							</li>
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>No monthly fees</span>
							</li>
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>Pay only for what you use</span>
							</li>
							<li class="flex items-center gap-3">
								<i class="fa-solid fa-check feature-check"></i>
								<span>Standard Support</span>
							</li>
						</ul>

						<div class="mt-8">
							<a href="register.html" class="btn w-full inline-flex items-center justify-center rounded-lg border border-brand/40 px-6 py-3 text-sm font-semibold text-brand hover:bg-brand/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up</a>
						</div>
					</article>
				</div>
			</div>
		</section>

		<!-- Features Comparison -->
		<section aria-labelledby="features-title" class="py-14 sm:py-18 lg:py-24 bg-paper">
			<div class="max-w-7xl mx-auto px-6">
				<h2 id="features-title" class="text-center font-serif text-3xl sm:text-4xl font-black text-ink mb-12">
					Compare <span class="text-brand">Features</span>
				</h2>

				<div class="overflow-x-auto">
					<table class="w-full bg-white rounded-2xl border border-black/10 shadow-card">
						<thead>
							<tr class="border-b border-black/10">
								<th class="text-left p-6 font-serif font-bold text-ink">Features</th>
								<th class="text-center p-6 font-serif font-bold text-ink bg-brand/5">Subscription</th>
								<th class="text-center p-6 font-serif font-bold text-ink">Pay As You Go</th>
							</tr>
						</thead>
						<tbody class="text-sm">
							<tr class="border-b border-black/5">
								<td class="p-6 font-medium text-ink">Monthly Cost</td>
								<td class="text-center p-6 text-muted bg-brand/5"><span data-monthly="$49.99/month" data-yearly="$39.99/month (billed annually)">$49.99/month</span></td>
								<td class="text-center p-6 text-muted">$0/month</td>
							</tr>
							<tr class="border-b border-black/5">
								<td class="p-6 font-medium text-ink">Included Requests</td>
								<td class="text-center p-6 text-muted bg-brand/5"><span data-monthly="100/month" data-yearly="1,200/year">100/month</span></td>
								<td class="text-center p-6 text-muted">None</td>
							</tr>
							<tr class="border-b border-black/5">
								<td class="p-6 font-medium text-ink">Additional Request Cost</td>
								<td class="text-center p-6 text-muted bg-brand/5">$0.99 each</td>
								<td class="text-center p-6 text-muted">$1.99 each</td>
							</tr>
							<tr class="border-b border-black/5">
								<td class="p-6 font-medium text-ink">REST API Access</td>
								<td class="text-center p-6 bg-brand/5"><i class="fa-solid fa-check feature-check"></i></td>
								<td class="text-center p-6"><i class="fa-solid fa-check feature-check"></i></td>
							</tr>
							<tr class="border-b border-black/5">
								<td class="p-6 font-medium text-ink">Web Form Verification</td>
								<td class="text-center p-6 bg-brand/5"><i class="fa-solid fa-check feature-check"></i></td>
								<td class="text-center p-6"><i class="fa-solid fa-check feature-check"></i></td>
							</tr>
							<tr class="border-b border-black/5">
								<td class="p-6 font-medium text-ink">HTTPS Encryption</td>
								<td class="text-center p-6 bg-brand/5"><i class="fa-solid fa-check feature-check"></i></td>
								<td class="text-center p-6"><i class="fa-solid fa-check feature-check"></i></td>
							</tr>
							<tr class="border-b border-black/5">
								<td class="p-6 font-medium text-ink">Support Level</td>
								<td class="text-center p-6 text-muted bg-brand/5">Priority</td>
								<td class="text-center p-6 text-muted">Standard</td>
							</tr>
							<tr>
								<td class="p-6 font-medium text-ink">Monthly Commitment</td>
								<td class="text-center p-6 text-muted bg-brand/5">Required</td>
								<td class="text-center p-6 text-muted">None</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</section>

		<!-- API Demo Section -->
		<section aria-labelledby="demo-title" class="py-14 sm:py-18 lg:py-24 bg-white">
			<div class="max-w-7xl mx-auto px-6">
				<h2 id="demo-title" class="text-center font-serif text-3xl sm:text-4xl font-black text-ink mb-12">
					Try Our <span class="text-brand">API</span>
				</h2>

				<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
					<div>
						<h3 class="font-serif text-2xl font-bold text-ink mb-4">REST API Integration</h3>
						<p class="text-muted mb-6">
							Simple HTTP requests to verify any African business. Get instant access to registration
							status, tax information, and more.
						</p>

						<div class="bg-ink rounded-lg p-6 text-white font-mono text-sm overflow-x-auto">
							<div class="text-green-400 mb-2"># Example API Request</div>
							<div class="text-blue-300">curl -X POST \</div>
							<div class="text-blue-300 ml-4">"https://api.konspect.app/v1/verify" \</div>
							<div class="text-blue-300 ml-4">-H "Authorization: Bearer YOUR_API_KEY" \</div>
							<div class="text-blue-300 ml-4">-H "Content-Type: application/json" \</div>
							<div class="text-blue-300 ml-4">-d '{</div>
							<div class="text-blue-300 ml-8">"company_name": "ECOBANK TOGO",</div>
							<div class="text-blue-300 ml-8">"rccm": "TG-LOM-01-2023-B12-00001",</div>
							<div class="text-blue-300 ml-8">"nif": "*************",</div>
							<div class="text-blue-300 ml-8">"country": "TG"</div>
							<div class="text-blue-300 ml-4">}'</div>
						</div>

						<div class="mt-6">
							<a href="demo.html"
								class="btn inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">
								<i class="fa-solid fa-play mr-2"></i>
								Try Live Demo
							</a>
						</div>
					</div>

					<div>
						<h3 class="font-serif text-2xl font-bold text-ink mb-4">Web Form Verification</h3>
						<p class="text-muted mb-6">
							No coding required. Use our web interface to verify businesses manually with the same
							powerful data.
						</p>

						<div class="bg-paper rounded-lg border border-black/10 p-6">
							<form class="space-y-4" id="verificationForm">
								<div>
									<label for="companyName" class="block text-sm font-medium text-ink mb-2">Company Name</label>
									<input type="text" id="companyName" name="companyName" placeholder="e.g., ECOBANK TOGO"
										class="w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:ring-2 focus:ring-brand/60 focus:border-brand">
								</div>
								<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<label for="rccm" class="block text-sm font-medium text-ink mb-2">RCCM Number</label>
										<input type="text" id="rccm" name="rccm" placeholder="e.g., TG-LOM-01-2023-B12-00001"
											class="w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:ring-2 focus:ring-brand/60 focus:border-brand">
									</div>
									<div>
										<label for="nif" class="block text-sm font-medium text-ink mb-2">NIF Number</label>
										<input type="text" id="nif" name="nif" placeholder="e.g., *************"
											class="w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:ring-2 focus:ring-brand/60 focus:border-brand">
									</div>
								</div>
								<div>
									<label for="country" class="block text-sm font-medium text-ink mb-2">Country</label>
									<select id="country" name="country"
										class="w-full rounded-lg border border-black/20 px-4 py-3 text-ink focus:ring-2 focus:ring-brand/60 focus:border-brand">
										<option value="">Select country</option>
										<option value="BJ">Benin</option>
										<option value="BF">Burkina Faso</option>
										<option value="CM">Cameroon</option>
										<option value="CF">Central African Republic</option>
										<option value="TD">Chad</option>
										<option value="KM">Comoros</option>
										<option value="CG">Republic of the Congo</option>
										<option value="CI">Côte d'Ivoire</option>
										<option value="GQ">Equatorial Guinea</option>
										<option value="GA">Gabon</option>
										<option value="GN">Guinea</option>
										<option value="GW">Guinea-Bissau</option>
										<option value="ML">Mali</option>
										<option value="NE">Niger</option>
										<option value="SN">Senegal</option>
										<option value="TG">Togo</option>
									</select>
								</div>
								<button type="submit"
									class="btn w-full inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">
									<i class="fa-solid fa-search mr-2"></i>
									Verify Business
								</button>
							</form>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- FAQ Section -->
		<section aria-labelledby="faq-title" class="py-14 sm:py-18 lg:py-24 bg-paper">
			<div class="max-w-4xl mx-auto px-6">
				<h2 id="faq-title" class="text-center font-serif text-3xl sm:text-4xl font-black text-ink mb-12">
					Frequently Asked <span class="text-brand">Questions</span>
				</h2>

				<div class="space-y-6">
					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>What countries are covered by your API?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>We cover all OHADA member countries including Benin, Burkina Faso, Cameroon, Central
								African Republic, Chad, Comoros, Republic of the Congo, Côte d'Ivoire, Equatorial
								Guinea, Gabon, Guinea, Guinea-Bissau, Mali, Niger, Senegal, and Togo.</p>
						</div>
					</details>

					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>How accurate is your business data?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>Our data is sourced directly from official government registries and updated in
								real-time. We maintain a 99.5% accuracy rate and provide data freshness timestamps with
								every response.</p>
						</div>
					</details>

					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>Can I upgrade or downgrade my plan?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>Yes, you can change your plan at any time. Upgrades take effect immediately, while
								downgrades take effect at the next billing cycle. Unused requests don't roll over
								between billing periods.</p>
						</div>
					</details>

					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>What happens if I exceed my request limit?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>If you exceed your monthly limit, additional requests are charged at $1.99 per request.
								You'll receive notifications at 80% and 95% of your limit to help you manage usage.</p>
						</div>
					</details>

					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>Do you offer API documentation and support?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>Yes, we provide comprehensive API documentation, code examples in multiple languages, and
								dedicated support. Professional and Enterprise plans include priority support with
								faster response times.</p>
						</div>
					</details>

					<details class="group bg-white rounded-lg border border-black/10 shadow-card">
						<summary
							class="flex items-center justify-between p-6 cursor-pointer font-medium text-ink hover:text-brand focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded-lg">
							<span>Can I test the service before signing up?</span>
							<i class="fa-solid fa-chevron-down transition-transform group-open:rotate-180"></i>
						</summary>
						<div class="px-6 pb-6 text-muted">
							<p>Yes, you can use our free demo page to test business verification functionality before signing up for a paid plan. The demo provides a realistic preview of our service capabilities.</p>
						</div>
					</details>
				</div>
			</div>
		</section>

		<!-- CTA Section -->
		<section class="py-14 sm:py-18 lg:py-24 bg-white">
			<div class="max-w-4xl mx-auto px-6 text-center">
				<h2 class="font-serif text-3xl sm:text-4xl font-black text-ink mb-6">
					Ready to Get <span class="text-brand">Started?</span>
				</h2>
				<p class="text-lg text-muted mb-8 max-w-2xl mx-auto">
					Join thousands of businesses already using Konspect to verify African companies and streamline their
					operations.
				</p>
				<div class="flex flex-wrap gap-4 justify-center">
					<a href="register.html"
						class="btn inline-flex items-center justify-center rounded-lg bg-brand px-8 py-4 text-base font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">
						Sign Up Now
					</a>
					<a href="demo.html"
						class="btn inline-flex items-center justify-center rounded-lg border border-brand/40 px-8 py-4 text-base font-semibold text-brand hover:bg-brand/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">
						Try Demo
					</a>
				</div>
			</div>
		</section>
	</main>

	<!-- Footer -->
	<footer class="bg-brand text-paper" id="contact">
		<div class="max-w-7xl mx-auto px-6 pt-12 pb-10">
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
				<section aria-labelledby="office-title">
					<h3 id="office-title" class="font-semibold text-paper">Head Office</h3>
					<ul class="mt-4 space-y-1 text-sm/6 text-white/90">
						<li>14 Rue adjinamoto</li>
						<li>Qt Avenou</li>
						<li>Lome</li>
						<li>Togo</li>
						<li>05 BP: 986</li>
					</ul>
				</section>

				<section aria-labelledby="policies-title">
					<h3 id="policies-title" class="font-semibold text-paper">Policies and Documentation</h3>
					<ul class="mt-4 grid grid-cols-2 gap-x-6 gap-y-1 text-sm/6 text-white/90">
						<li><a href="#" class="hover:underline">Cookie policy</a></li>
						<li><a href="#" class="hover:underline">Information Security</a></li>
						<li><a href="#" class="hover:underline">Modern Slavery Act</a></li>
						<li><a href="#" class="hover:underline">Privacy Policy</a></li>
						<li><a href="#" class="hover:underline">Quality Policy</a></li>
						<li><a href="#" class="hover:underline">Terms and Conditions</a></li>
					</ul>
				</section>

				<section aria-labelledby="social-title">
					<h3 id="social-title" class="font-semibold text-paper">Social Media</h3>
					<div class="mt-4 flex items-center gap-4 text-lg">
						<a aria-label="Facebook" href="#"
							class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i
								class="fab fa-facebook-f"></i></a>
						<a aria-label="Instagram" href="#"
							class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i
								class="fab fa-instagram"></i></a>
						<a aria-label="WhatsApp" href="#"
							class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i
								class="fab fa-whatsapp"></i></a>
						<a aria-label="YouTube" href="#"
							class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i
								class="fab fa-youtube"></i></a>
					</div>
				</section>

				<section aria-labelledby="newsletter-title">
					<h3 id="newsletter-title" class="font-semibold text-paper">Subscribe to Newsletter</h3>
					<form class="mt-4 flex gap-3" action="#" method="get">
						<label for="newsletter-email" class="sr-only">Email Address</label>
						<input id="newsletter-email" type="email" required placeholder="Email Address"
							class="w-full rounded-lg border-0 px-4 py-2.5 text-ink placeholder:text-muted/70 focus:ring-2 focus:ring-brand/60">
						<button type="submit"
							class="btn rounded-lg bg-white px-4 py-2.5 text-sm font-semibold text-brand hover:bg-white/90">Subscribe</button>
					</form>
				</section>
			</div>

			<div class="mt-12 border-t border-white/20 pt-6 text-xs text-white/80">
				<p>&copy; 2023 Konspect Sarl. Registered in Togo, NIF: XXXXXXXX, RCCM: </p>
			</div>
		</div>
	</footer>

	<!-- Scripts: tiny interactivity only -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/js/all.min.js"></script>
	<script>
			// Mobile menu toggle
			(function () {
				const btn = document.getElementById('mobileMenuBtn');
				const menu = document.getElementById('mobileMenu');
				if (!btn || !menu) return;
				btn.addEventListener('click', () => {
					const expanded = btn.getAttribute('aria-expanded') === 'true';
					btn.setAttribute('aria-expanded', String(!expanded));
					menu.classList.toggle('hidden');
				});
			})();

		// Billing toggle functionality
		(function () {
			const toggle = document.getElementById('billingToggle');
			const monthlyElements = document.querySelectorAll('[data-monthly]');
			const yearlyElements = document.querySelectorAll('[data-yearly]');

			if (!toggle) return;

			let isYearly = false;

			function updatePricing() {
				const toggleButton = toggle.querySelector('span');

				if (isYearly) {
					toggle.classList.add('bg-brand');
					toggle.classList.remove('bg-gray-200');
					toggleButton.classList.add('translate-x-6');
					toggleButton.classList.remove('translate-x-1');
					toggle.setAttribute('aria-checked', 'true');

					// Update pricing display
					monthlyElements.forEach(el => {
						if (el.dataset.yearly) {
							el.textContent = el.dataset.yearly;
						}
					});
				} else {
					toggle.classList.remove('bg-brand');
					toggle.classList.add('bg-gray-200');
					toggleButton.classList.remove('translate-x-6');
					toggleButton.classList.add('translate-x-1');
					toggle.setAttribute('aria-checked', 'false');

					// Update pricing display
					monthlyElements.forEach(el => {
						if (el.dataset.monthly) {
							el.textContent = el.dataset.monthly;
						}
					});
				}
			}

			toggle.addEventListener('click', () => {
				isYearly = !isYearly;
				updatePricing();
			});

			// Initialize
			updatePricing();
		})();

		// Form verification demo
		(function () {
			const form = document.getElementById('verificationForm');
			if (!form) return;

			form.addEventListener('submit', (e) => {
				e.preventDefault();

				const companyName = document.getElementById('companyName').value;
				const rccm = document.getElementById('rccm').value;
				const nif = document.getElementById('nif').value;
				const country = document.getElementById('country').value;

				if (!companyName || !country) {
					alert('Please fill in at least Company Name and Country');
					return;
				}

				// Simulate API call
				const button = form.querySelector('button[type="submit"]');
				const originalText = button.innerHTML;

				button.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Verifying...';
				button.disabled = true;

				setTimeout(() => {
					// Simulate response
					const mockResponse = {
						company_name: companyName,
						rccm: rccm || 'TG-LOM-01-2023-B12-' + Math.random().toString().substr(2, 5),
						nif: nif || '********' + Math.random().toString().substr(2, 5),
						country: country,
						status: 'active',
						registration_date: '2023-01-15',
						tax_status: 'compliant',
						business_type: 'Commercial Bank',
						address: 'Avenue de la Liberation, Lomé',
						last_updated: new Date().toISOString().split('T')[0]
					};

					alert(`Verification Result:\n\nCompany: ${mockResponse.company_name}\nRCCM: ${mockResponse.rccm}\nNIF: ${mockResponse.nif}\nCountry: ${mockResponse.country}\nStatus: ${mockResponse.status}\nBusiness Type: ${mockResponse.business_type}\nRegistration Date: ${mockResponse.registration_date}\nTax Status: ${mockResponse.tax_status}\nAddress: ${mockResponse.address}\nLast Updated: ${mockResponse.last_updated}\n\nThis is a demo response. Sign up for real data!`);

					button.innerHTML = originalText;
					button.disabled = false;
				}, 2000);
			});
		})();
	</script>
</body>

</html>