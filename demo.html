<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Free Demo — Konspect</title>

	<!-- Google Fonts: <PERSON><PERSON> (brand), <PERSON><PERSON> (body), <PERSON><PERSON><PERSON><PERSON> (headings) -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@600;700&family=Lato:wght@300;400;700;900&family=Merriweather:wght@700;900&display=swap" rel="stylesheet">

	<!-- Font Awesome (for icons) -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">

	<!-- Tailwind CSS CDN -->
	<script src="https://cdn.tailwindcss.com"></script>
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						brand: '#FF6039',
						ink: '#1D1D1D',
						muted: '#585D62',
						paper: '#FAFAFA'
					},
					fontFamily: {
						sans: ['Lato', 'ui-sans-serif', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
						serif: ['Merriweather', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
						fredoka: ['Fredoka', 'system-ui', 'sans-serif']
					}
				}
			}
		}
	</script>

	<style>
		:root { --brand: #FF6039; --ink:#1D1D1D; --muted:#585D62; --paper:#FAFAFA; }
		html { scroll-behavior: smooth; }
		body { font-family: Lato, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif; color: var(--muted); background-color: var(--paper); }
		h1, h2, h3, h4, h5, h6 { font-family: Merriweather, Georgia, Cambria, Times New Roman, Times, serif; color: var(--ink); }
		.brand-font { font-family: Fredoka, system-ui, sans-serif; }
		.shadow-card { box-shadow: 0 16px 40px rgba(16,24,40,.08), 0 4px 10px rgba(16,24,40,.06); }
		.btn { transition: transform .08s ease, box-shadow .2s ease; }
		.btn:active { transform: translateY(1px); }
		.btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
		
		/* Loading animation */
		.loading-spinner {
			border: 2px solid #f3f3f3;
			border-top: 2px solid var(--brand);
			border-radius: 50%;
			width: 20px;
			height: 20px;
			animation: spin 1s linear infinite;
		}
		
		@keyframes spin {
			0% { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}
		
		/* Form validation styles */
		.field-error { border-color: #ef4444; }
		.field-success { border-color: #10b981; }
		
		/* Results animation */
		.fade-in {
			animation: fadeIn 0.5s ease-in-out;
		}
		
		@keyframes fadeIn {
			from { opacity: 0; transform: translateY(20px); }
			to { opacity: 1; transform: translateY(0); }
		}
	</style>
</head>

<body class="bg-white antialiased">
	<!-- Navbar -->
	<header class="border-b border-black/5 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 sticky top-0 z-50">
		<nav class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between" aria-label="Primary">
			<a href="index.html" class="flex items-center gap-2 group" aria-label="Konspect home">
				<span class="brand-font text-2xl font-bold tracking-tight text-brand">Konspect</span>
			</a>
			<ul class="hidden md:flex items-center gap-8 text-sm text-muted">
				<li><a href="index.html#plans" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Pricing</a></li>
				<li><a href="index.html#docs" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Documentation</a></li>
				<li><a href="index.html#contact" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Contact Us</a></li>
			</ul>
			<div class="hidden md:flex items-center gap-3">
				<a href="login.html" class="btn inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Log In</a>
				<a href="register.html" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up</a>
			</div>
			<button id="mobileMenuBtn" class="md:hidden inline-flex h-10 w-10 items-center justify-center rounded-lg border border-black/10 text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60" aria-controls="mobileMenu" aria-expanded="false" aria-label="Open menu">
				<i class="fa-solid fa-bars"></i>
			</button>
		</nav>
		<div id="mobileMenu" class="md:hidden hidden border-t border-black/5">
			<div class="max-w-7xl mx-auto px-6 py-4 space-y-3">
				<a href="index.html#plans" class="block">Pricing</a>
				<a href="index.html#docs" class="block">Documentation</a>
				<a href="index.html#contact" class="block">Contact Us</a>
				<div class="pt-3 flex gap-3">
					<a href="login.html" class="btn flex-1 inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink">Log In</a>
					<a href="register.html" class="btn flex-1 inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white">Sign Up</a>
				</div>
			</div>
		</div>
	</header>

	<main class="bg-paper">
		<!-- Hero Section -->
		<section class="py-12 md:py-16 lg:py-20">
			<div class="max-w-4xl mx-auto px-6 text-center">
				<h1 class="text-4xl sm:text-5xl xl:text-6xl font-black leading-tight tracking-tight text-ink">
					Try Our <span class="text-brand">Free Demo</span>
				</h1>
				<p class="mt-5 text-lg text-muted max-w-2xl mx-auto">
					Search for any African company and instantly access comprehensive business information. No registration required for this demo.
				</p>
			</div>
		</section>

		<!-- Search Form Section -->
		<section class="pb-12">
			<div class="max-w-2xl mx-auto px-6">
				<form id="searchForm" class="bg-white rounded-2xl border border-black/10 p-8 shadow-card">
					<div class="space-y-6">
						<div>
							<label for="companyName" class="block text-sm font-semibold text-ink mb-2">Company Name *</label>
							<input 
								type="text" 
								id="companyName" 
								name="companyName" 
								required 
								placeholder="Enter company name"
								class="w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:border-brand focus:ring-2 focus:ring-brand/20 focus:outline-none transition-colors"
							>
							<p class="mt-1 text-xs text-muted">Enter the full or partial company name</p>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label for="rccm" class="flex items-center gap-2 text-sm font-semibold text-ink mb-2">
									RCCM Number *
									<div class="relative group">
										<i class="fas fa-info-circle text-muted hover:text-brand cursor-help"></i>
										<div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-ink text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
											Trade and Credit Register Number<br>
											Format: CC-VVV-SS-YYYY-XXX-NNNNN
											<div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-ink"></div>
										</div>
									</div>
								</label>
								<input
									type="text"
									id="rccm"
									name="rccm"
									required
									placeholder="e.g., TG-LOM-01-2023-B12-00001"
									class="w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:border-brand focus:ring-2 focus:ring-brand/20 focus:outline-none transition-colors"
								>
								<p class="mt-1 text-xs text-muted">Trade and Credit Register identification number</p>
							</div>
							<div>
								<label for="nif" class="flex items-center gap-2 text-sm font-semibold text-ink mb-2">
									NIF Number *
									<div class="relative group">
										<i class="fas fa-info-circle text-muted hover:text-brand cursor-help"></i>
										<div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-ink text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
											Tax Identification Number<br>
											Usually 11 digits for companies
											<div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-ink"></div>
										</div>
									</div>
								</label>
								<input
									type="text"
									id="nif"
									name="nif"
									required
									placeholder="e.g., 12345678901"
									class="w-full rounded-lg border border-black/20 px-4 py-3 text-ink placeholder:text-muted/70 focus:border-brand focus:ring-2 focus:ring-brand/20 focus:outline-none transition-colors"
								>
								<p class="mt-1 text-xs text-muted">Tax identification number assigned by tax authorities</p>
							</div>
						</div>

						<div>
							<label for="country" class="block text-sm font-semibold text-ink mb-2">Country *</label>
							<select 
								id="country" 
								name="country" 
								required
								class="w-full rounded-lg border border-black/20 px-4 py-3 text-ink focus:border-brand focus:ring-2 focus:ring-brand/20 focus:outline-none transition-colors"
							>
								<option value="">Select a country</option>
								<option value="BJ">Benin</option>
								<option value="BF">Burkina Faso</option>
								<option value="CM">Cameroon</option>
								<option value="CF">Central African Republic</option>
								<option value="TD">Chad</option>
								<option value="KM">Comoros</option>
								<option value="CG">Congo</option>
								<option value="CI">Côte d'Ivoire</option>
								<option value="DJ">Djibouti</option>
								<option value="GQ">Equatorial Guinea</option>
								<option value="GA">Gabon</option>
								<option value="GN">Guinea</option>
								<option value="GW">Guinea-Bissau</option>
								<option value="ML">Mali</option>
								<option value="MR">Mauritania</option>
								<option value="NE">Niger</option>
								<option value="SN">Senegal</option>
								<option value="TG">Togo</option>
							</select>
						</div>

						<button 
							type="submit" 
							id="searchBtn"
							class="btn w-full inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white hover:bg-brand/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 disabled:opacity-60 disabled:cursor-not-allowed"
						>
							<span id="searchBtnText">Search Company</span>
							<div id="searchBtnSpinner" class="loading-spinner ml-2 hidden"></div>
						</button>
					</div>
				</form>
			</div>
		</section>

		<!-- Results Section -->
		<section id="resultsSection" class="pb-16 hidden">
			<div class="max-w-4xl mx-auto px-6">
				<div id="resultsContainer" class="bg-white rounded-2xl border border-black/10 shadow-card overflow-hidden">
					<!-- Results will be populated here -->
				</div>
			</div>
		</section>
	</main>

	<!-- Footer -->
	<footer class="bg-brand text-paper" id="contact">
		<div class="max-w-7xl mx-auto px-6 pt-12 pb-10">
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
				<section aria-labelledby="office-title">
					<h3 id="office-title" class="font-semibold text-paper">Head Office</h3>
					<ul class="mt-4 space-y-1 text-sm/6 text-white/90">
						<li>14 Rue adjinamoto</li>
<li>Qt Avenou</li>
<li>Lome</li>
<li>Togo</li>
<li>05 BP: 986</li>
					</ul>
				</section>

				<section aria-labelledby="policies-title">
					<h3 id="policies-title" class="font-semibold text-paper">Policies and Documentation</h3>
					<ul class="mt-4 grid grid-cols-2 gap-x-6 gap-y-1 text-sm/6 text-white/90">
						<li><a href="#" class="hover:underline">Cookie policy</a></li>
						<li><a href="#" class="hover:underline">Information Security</a></li>
						<li><a href="#" class="hover:underline">Modern Slavery Act</a></li>
						<li><a href="#" class="hover:underline">Privacy Policy</a></li>
						<li><a href="#" class="hover:underline">Quality Policy</a></li>
						<li><a href="#" class="hover:underline">Terms and Conditions</a></li>
					</ul>
				</section>

				<section aria-labelledby="social-title">
					<h3 id="social-title" class="font-semibold text-paper">Social Media</h3>
					<div class="mt-4 flex items-center gap-4 text-lg">
						<a aria-label="Facebook" href="#" class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i class="fab fa-facebook-f"></i></a>
						<a aria-label="Instagram" href="#" class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i class="fab fa-instagram"></i></a>
						<a aria-label="WhatsApp" href="#" class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i class="fab fa-whatsapp"></i></a>
						<a aria-label="YouTube" href="#" class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i class="fab fa-youtube"></i></a>
					</div>
				</section>

				<section aria-labelledby="newsletter-title">
					<h3 id="newsletter-title" class="font-semibold text-paper">Subscribe to Newsletter</h3>
					<form class="mt-4 flex gap-3" action="#" method="get">
						<label for="newsletter-email" class="sr-only">Email Address</label>
						<input id="newsletter-email" type="email" required placeholder="Email Address" class="w-full rounded-lg border-0 px-4 py-2.5 text-ink placeholder:text-muted/70 focus:ring-2 focus:ring-brand/60">
						<button type="submit" class="btn rounded-lg bg-white px-4 py-2.5 text-sm font-semibold text-brand hover:bg-white/90">Subscribe</button>
					</form>
				</section>
			</div>

			<div class="mt-12 border-t border-white/20 pt-6 text-xs text-white/80">
				<p>&copy; 2023 Konspect Sarl. Registered in Togo, NIF: XXXXXXXX, RCCM: </p>
			</div>
		</div>
	</footer>

	<!-- Scripts -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/js/all.min.js"></script>
	<script>
		// Mobile menu toggle
		(function(){
			const btn = document.getElementById('mobileMenuBtn');
			const menu = document.getElementById('mobileMenu');
			if(!btn || !menu) return;
			btn.addEventListener('click', () => {
				const expanded = btn.getAttribute('aria-expanded') === 'true';
				btn.setAttribute('aria-expanded', String(!expanded));
				menu.classList.toggle('hidden');
			});
		})();

		// Demo data - simulated company information
		const demoCompanies = {
			'konspect': {
				name: 'Konspect Sarl',
				rccm: 'TG-LOM-01-2023-B12-00001',
				nif: '12345678901',
				country: 'TG',
				registrationDate: '2023-01-15',
				founder: {
					name: 'Samuel Kpassegna',
					email: '<EMAIL>',
					phone: '+228 90 12 34 56'
				},
				address: 'Lomé, Togo',
				taxStatus: 'Active',
				companyStatus: 'Active',
				legalStatus: 'SARL (Société à Responsabilité Limitée)',
				shareholders: [
					{ name: 'Samuel Kpassegna', percentage: '60%', role: 'Founder & CEO' },
					{ name: 'Marie Adjovi', percentage: '25%', role: 'Co-founder' },
					{ name: 'Tech Ventures Ltd', percentage: '15%', role: 'Investor' }
				],
				activities: ['Software Development', 'Data Services', 'Business Intelligence'],
				capital: '5,000,000 XOF',
				employees: '15-25',
				verified: true
			},
			'ecobank': {
				name: 'Ecobank Togo',
				rccm: 'TG-LOM-01-1988-B12-00156',
				nif: '***********',
				country: 'TG',
				registrationDate: '1988-03-20',
				founder: {
					name: 'Ecobank Group',
					email: '<EMAIL>',
					phone: '+228 22 21 42 42'
				},
				address: 'Avenue du 24 Janvier, Lomé, Togo',
				taxStatus: 'Active',
				companyStatus: 'Active',
				legalStatus: 'SA (Société Anonyme)',
				shareholders: [
					{ name: 'Ecobank Transnational Inc.', percentage: '100%', role: 'Parent Company' }
				],
				activities: ['Banking Services', 'Financial Services', 'Investment Banking'],
				capital: '15,000,000,000 XOF',
				employees: '500+',
				verified: true
			},
			'orange': {
				name: 'Orange Togo SA',
				rccm: 'TG-LOM-01-2010-B12-00789',
				nif: '***********',
				country: 'TG',
				registrationDate: '2010-07-12',
				founder: {
					name: 'Orange Group',
					email: '<EMAIL>',
					phone: '+228 90 00 11 11'
				},
				address: 'Boulevard du 13 Janvier, Lomé, Togo',
				taxStatus: 'Active',
				companyStatus: 'Active',
				legalStatus: 'SA (Société Anonyme)',
				shareholders: [
					{ name: 'Orange SA France', percentage: '85%', role: 'Parent Company' },
					{ name: 'Government of Togo', percentage: '15%', role: 'State Participation' }
				],
				activities: ['Telecommunications', 'Mobile Services', 'Internet Services'],
				capital: '25,000,000,000 XOF',
				employees: '1000+',
				verified: true
			}
		};

		// Search form handling
		document.getElementById('searchForm').addEventListener('submit', function(e) {
			e.preventDefault();

			const formData = new FormData(this);
			const companyName = formData.get('companyName').toLowerCase().trim();
			const rccm = formData.get('rccm').trim();
			const nif = formData.get('nif').trim();
			const country = formData.get('country');

			// Validate required fields
			if (!companyName || !rccm || !nif || !country) {
				showError('Please fill in all required fields (Company Name, RCCM Number, NIF Number, and Country).');
				return;
			}

			// Show loading state
			showLoading(true);

			// Simulate API delay
			setTimeout(() => {
				const result = searchCompany(companyName, rccm, nif, country);
				showLoading(false);

				if (result) {
					displayResults(result, { companyName, rccm, nif, country });
				} else {
					showNoResults({ companyName, rccm, nif, country });
				}
			}, 1500);
		});

		function searchCompany(name, rccm, nif, country) {
			// Enhanced search logic for demo with mandatory fields
			for (const [key, company] of Object.entries(demoCompanies)) {
				// Check if country matches first (mandatory)
				if (company.country !== country) continue;

				// Check for exact RCCM or NIF match (both mandatory)
				if (company.rccm === rccm || company.nif === nif) {
					return company;
				}

				// Also check company name match with same country
				if (company.name.toLowerCase().includes(name.toLowerCase()) ||
					key.includes(name.toLowerCase())) {
					return company;
				}
			}
			return null;
		}

		function showLoading(show) {
			const btn = document.getElementById('searchBtn');
			const btnText = document.getElementById('searchBtnText');
			const spinner = document.getElementById('searchBtnSpinner');

			if (show) {
				btn.disabled = true;
				btnText.textContent = 'Searching...';
				spinner.classList.remove('hidden');
			} else {
				btn.disabled = false;
				btnText.textContent = 'Search Company';
				spinner.classList.add('hidden');
			}
		}

		function showError(message) {
			// Simple error display - could be enhanced with toast notifications
			alert(message);
		}

		function displayResults(company, searchParams) {
			const resultsSection = document.getElementById('resultsSection');
			const resultsContainer = document.getElementById('resultsContainer');

			// Check if search parameters match
			const isExactMatch = checkMatch(company, searchParams);

			resultsContainer.innerHTML = `
				<div class="p-8 fade-in">
					<!-- Match Status -->
					<div class="flex items-center gap-3 mb-6 p-4 rounded-lg ${isExactMatch ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}">
						<i class="fas ${isExactMatch ? 'fa-check-circle text-green-600' : 'fa-exclamation-triangle text-yellow-600'} text-xl"></i>
						<div>
							<h3 class="font-semibold text-ink">${isExactMatch ? 'Exact Match Found' : 'Partial Match Found'}</h3>
							<p class="text-sm text-muted">${isExactMatch ? 'All provided information matches our records.' : 'Some information matches, please verify details below.'}</p>
						</div>
					</div>

					<!-- Company Header -->
					<div class="border-b border-black/10 pb-6 mb-6">
						<div class="flex items-start justify-between">
							<div>
								<h2 class="text-2xl font-serif font-bold text-ink">${company.name}</h2>
								<p class="text-muted mt-1">${company.legalStatus}</p>
							</div>
							<div class="flex items-center gap-2 px-3 py-1 rounded-full ${company.verified ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
								<i class="fas ${company.verified ? 'fa-shield-check' : 'fa-shield'} text-sm"></i>
								<span class="text-xs font-semibold">${company.verified ? 'Verified' : 'Unverified'}</span>
							</div>
						</div>
					</div>

					<!-- Company Details Grid -->
					<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
						<!-- Basic Information -->
						<div class="space-y-6">
							<div>
								<h3 class="font-serif font-bold text-ink mb-4">Basic Information</h3>
								<div class="space-y-3">
									<div class="flex justify-between">
										<span class="text-muted">Registration Date:</span>
										<span class="font-semibold text-ink">${formatDate(company.registrationDate)}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">RCCM Number:</span>
										<span class="font-semibold text-ink">${company.rccm}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">NIF Number:</span>
										<span class="font-semibold text-ink">${company.nif}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">Capital:</span>
										<span class="font-semibold text-ink">${company.capital}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">Employees:</span>
										<span class="font-semibold text-ink">${company.employees}</span>
									</div>
								</div>
							</div>

							<!-- Status Information -->
							<div>
								<h3 class="font-serif font-bold text-ink mb-4">Status Information</h3>
								<div class="space-y-3">
									<div class="flex justify-between">
										<span class="text-muted">Company Status:</span>
										<span class="font-semibold ${company.companyStatus === 'Active' ? 'text-green-600' : 'text-red-600'}">${company.companyStatus}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">Tax Status:</span>
										<span class="font-semibold ${company.taxStatus === 'Active' ? 'text-green-600' : 'text-red-600'}">${company.taxStatus}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">Legal Form:</span>
										<span class="font-semibold text-ink">${company.legalStatus}</span>
									</div>
								</div>
							</div>
						</div>

						<!-- Contact & Location -->
						<div class="space-y-6">
							<div>
								<h3 class="font-serif font-bold text-ink mb-4">Founder Information</h3>
								<div class="space-y-3">
									<div class="flex justify-between">
										<span class="text-muted">Name:</span>
										<span class="font-semibold text-ink">${company.founder.name}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">Email:</span>
										<span class="font-semibold text-ink">${company.founder.email}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">Phone:</span>
										<span class="font-semibold text-ink">${company.founder.phone}</span>
									</div>
								</div>
							</div>

							<div>
								<h3 class="font-serif font-bold text-ink mb-4">Location</h3>
								<div class="space-y-3">
									<div class="flex justify-between">
										<span class="text-muted">Address:</span>
										<span class="font-semibold text-ink">${company.address}</span>
									</div>
									<div class="flex justify-between">
										<span class="text-muted">Country:</span>
										<span class="font-semibold text-ink">${getCountryName(company.country)}</span>
									</div>
								</div>
							</div>

							<div>
								<h3 class="font-serif font-bold text-ink mb-4">Activities</h3>
								<div class="flex flex-wrap gap-2">
									${company.activities.map(activity => `
										<span class="px-3 py-1 bg-brand/10 text-brand rounded-full text-sm font-medium">${activity}</span>
									`).join('')}
								</div>
							</div>
						</div>
					</div>

					<!-- Shareholders -->
					<div class="mt-8 pt-6 border-t border-black/10">
						<h3 class="font-serif font-bold text-ink mb-4">Shareholders & Associates</h3>
						<div class="space-y-3">
							${company.shareholders.map(shareholder => `
								<div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
									<div>
										<span class="font-semibold text-ink">${shareholder.name}</span>
										<span class="text-sm text-muted ml-2">(${shareholder.role})</span>
									</div>
									<span class="font-bold text-brand">${shareholder.percentage}</span>
								</div>
							`).join('')}
						</div>
					</div>

					<!-- Call to Action -->
					<div class="mt-8 pt-6 border-t border-black/10 text-center">
						<p class="text-muted mb-4">Want access to more detailed information and our full database?</p>
						<div class="flex flex-wrap gap-3 justify-center">
							<a href="register.html" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up Now</a>
							<a href="index.html#plans" class="btn inline-flex items-center justify-center rounded-lg border border-brand/40 px-6 py-3 text-sm font-semibold text-brand hover:bg-brand/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">View Pricing</a>
						</div>
					</div>
				</div>
			`;

			resultsSection.classList.remove('hidden');
			resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
		}

		function showNoResults(searchParams) {
			const resultsSection = document.getElementById('resultsSection');
			const resultsContainer = document.getElementById('resultsContainer');

			resultsContainer.innerHTML = `
				<div class="p-8 text-center fade-in">
					<div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
						<i class="fas fa-search text-2xl text-gray-400"></i>
					</div>
					<h3 class="text-xl font-serif font-bold text-ink mb-2">No Results Found</h3>
					<p class="text-muted mb-6">We couldn't find any companies matching your search criteria.</p>

					<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-left">
						<h4 class="font-semibold text-ink mb-2">Search Tips:</h4>
						<ul class="text-sm text-muted space-y-1">
							<li>• Double-check the RCCM number format (e.g., TG-LOM-01-2023-B12-00001)</li>
							<li>• Verify the NIF number (usually 11 digits)</li>
							<li>• Check the spelling of the company name</li>
							<li>• Make sure you selected the correct country</li>
							<li>• Ensure all information corresponds to the same company</li>
						</ul>
					</div>

					<div class="text-center">
						<p class="text-muted mb-4">This is a limited demo. Our full database contains 9.7M+ African businesses.</p>
						<div class="flex flex-wrap gap-3 justify-center">
							<a href="register.html" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Access Full Database</a>
							<button onclick="document.getElementById('searchForm').reset(); document.getElementById('resultsSection').classList.add('hidden');" class="btn inline-flex items-center justify-center rounded-lg border border-black/20 px-6 py-3 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Try Another Search</button>
						</div>
					</div>
				</div>
			`;

			resultsSection.classList.remove('hidden');
			resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
		}

		function checkMatch(company, searchParams) {
			let matches = 0;
			let total = 4; // All fields are now mandatory

			// Check company name (mandatory)
			if (company.name.toLowerCase().includes(searchParams.companyName.toLowerCase())) {
				matches++;
			}

			// Check RCCM (mandatory)
			if (company.rccm === searchParams.rccm) {
				matches++;
			}

			// Check NIF (mandatory)
			if (company.nif === searchParams.nif) {
				matches++;
			}

			// Check country (mandatory)
			if (company.country === searchParams.country) {
				matches++;
			}

			return matches === total; // Exact match only if all 4 fields match
		}

		function formatDate(dateString) {
			const date = new Date(dateString);
			return date.toLocaleDateString('en-US', {
				year: 'numeric',
				month: 'long',
				day: 'numeric'
			});
		}

		function getCountryName(code) {
			const countries = {
				'BJ': 'Benin',
				'BF': 'Burkina Faso',
				'CM': 'Cameroon',
				'CF': 'Central African Republic',
				'TD': 'Chad',
				'KM': 'Comoros',
				'CG': 'Congo',
				'CI': 'Côte d\'Ivoire',
				'DJ': 'Djibouti',
				'GQ': 'Equatorial Guinea',
				'GA': 'Gabon',
				'GN': 'Guinea',
				'GW': 'Guinea-Bissau',
				'ML': 'Mali',
				'MR': 'Mauritania',
				'NE': 'Niger',
				'SN': 'Senegal',
				'TG': 'Togo'
			};
			return countries[code] || code;
		}
	</script>
